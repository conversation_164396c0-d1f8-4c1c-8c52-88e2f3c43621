/**
 * 时间处理工具类
 * 提供时间格式化、计算等功能
 */
window.TimeUtils = {

  /**
   * 格式化秒数为可读的时间字符串
   * @param {number} seconds - 秒数
   * @param {boolean} showSeconds - 是否显示秒数
   * @returns {string} 格式化的时间字符串
   */
  formatTime(seconds, showSeconds = true) {
    if (typeof seconds !== 'number' || seconds < 0) {
      return '0s';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      if (showSeconds) {
        return `${hours}h ${minutes}m ${secs}s`;
      } else {
        return `${hours}h ${minutes}m`;
      }
    } else if (minutes > 0) {
      if (showSeconds) {
        return `${minutes}m ${secs}s`;
      } else {
        return `${minutes}m`;
      }
    } else {
      return `${secs}s`;
    }
  },

  /**
   * 格式化秒数为计时器显示格式 (HH:MM:SS)
   * @param {number} seconds - 秒数
   * @returns {string} 计时器格式的时间字符串
   */
  formatTimerDisplay(seconds) {
    if (typeof seconds !== 'number' || seconds < 0) {
      return '00:00:00';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  /**
   * 格式化秒数为简短格式
   * @param {number} seconds - 秒数
   * @returns {string} 简短格式的时间字符串
   */
  formatTimeShort(seconds) {
    if (typeof seconds !== 'number' || seconds < 0) {
      return '0s';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else {
      return `${Math.floor(seconds)}s`;
    }
  },

  /**
   * 解析时间字符串为秒数
   * 支持格式: "1h 30m 45s", "90m", "3600s" 等
   * @param {string} timeString - 时间字符串
   * @returns {number} 秒数
   */
  parseTimeString(timeString) {
    if (typeof timeString !== 'string') {
      return 0;
    }

    let totalSeconds = 0;
    
    // 匹配小时
    const hoursMatch = timeString.match(/(\d+)h/);
    if (hoursMatch) {
      totalSeconds += parseInt(hoursMatch[1]) * 3600;
    }

    // 匹配分钟
    const minutesMatch = timeString.match(/(\d+)m/);
    if (minutesMatch) {
      totalSeconds += parseInt(minutesMatch[1]) * 60;
    }

    // 匹配秒
    const secondsMatch = timeString.match(/(\d+)s/);
    if (secondsMatch) {
      totalSeconds += parseInt(secondsMatch[1]);
    }

    return totalSeconds;
  },

  /**
   * 获取当前时间戳（毫秒）
   * @returns {number} 当前时间戳
   */
  now() {
    return Date.now();
  },

  /**
   * 获取当前时间戳（秒）
   * @returns {number} 当前时间戳（秒）
   */
  nowSeconds() {
    return Math.floor(Date.now() / 1000);
  },

  /**
   * 格式化日期时间
   * @param {Date|number} date - 日期对象或时间戳
   * @param {string} format - 格式字符串，默认 'YYYY-MM-DD HH:mm:ss'
   * @returns {string} 格式化的日期时间字符串
   */
  formatDateTime(date = new Date(), format = 'YYYY-MM-DD HH:mm:ss') {
    const d = date instanceof Date ? date : new Date(date);
    
    if (isNaN(d.getTime())) {
      return '';
    }

    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    const hours = d.getHours().toString().padStart(2, '0');
    const minutes = d.getMinutes().toString().padStart(2, '0');
    const seconds = d.getSeconds().toString().padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  },

  /**
   * 计算两个时间戳之间的差值（秒）
   * @param {number} startTime - 开始时间戳（毫秒）
   * @param {number} endTime - 结束时间戳（毫秒），默认为当前时间
   * @returns {number} 时间差（秒）
   */
  getDuration(startTime, endTime = Date.now()) {
    return Math.floor((endTime - startTime) / 1000);
  },

  /**
   * 创建一个简单的计时器类
   * @returns {Object} 计时器对象
   */
  createTimer() {
    return {
      startTime: null,
      pausedTime: 0,
      isRunning: false,
      isPaused: false,

      /**
       * 开始计时
       */
      start() {
        if (!this.isRunning) {
          this.startTime = Date.now() - this.pausedTime;
          this.isRunning = true;
          this.isPaused = false;
        }
      },

      /**
       * 暂停计时
       */
      pause() {
        if (this.isRunning && !this.isPaused) {
          this.pausedTime = Date.now() - this.startTime;
          this.isPaused = true;
        }
      },

      /**
       * 恢复计时
       */
      resume() {
        if (this.isRunning && this.isPaused) {
          this.startTime = Date.now() - this.pausedTime;
          this.isPaused = false;
        }
      },

      /**
       * 停止计时
       * @returns {number} 总计时时间（秒）
       */
      stop() {
        if (this.isRunning) {
          const totalTime = this.isPaused ? this.pausedTime : (Date.now() - this.startTime);
          this.reset();
          return Math.floor(totalTime / 1000);
        }
        return 0;
      },

      /**
       * 重置计时器
       */
      reset() {
        this.startTime = null;
        this.pausedTime = 0;
        this.isRunning = false;
        this.isPaused = false;
      },

      /**
       * 获取当前计时时间（秒）
       * @returns {number} 当前计时时间（秒）
       */
      getElapsed() {
        if (!this.isRunning) {
          return 0;
        }
        
        if (this.isPaused) {
          return Math.floor(this.pausedTime / 1000);
        }
        
        return Math.floor((Date.now() - this.startTime) / 1000);
      },

      /**
       * 获取格式化的当前计时时间
       * @returns {string} 格式化的时间字符串
       */
      getFormattedElapsed() {
        return TimeUtils.formatTimerDisplay(this.getElapsed());
      }
    };
  },

  /**
   * 延迟执行函数
   * @param {number} ms - 延迟时间（毫秒）
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 检查是否为有效的时间值
   * @param {*} value - 要检查的值
   * @returns {boolean} 是否为有效时间
   */
  isValidTime(value) {
    if (typeof value === 'number') {
      return !isNaN(value) && value >= 0;
    }
    
    if (value instanceof Date) {
      return !isNaN(value.getTime());
    }
    
    return false;
  },

  /**
   * 将毫秒转换为秒
   * @param {number} milliseconds - 毫秒数
   * @returns {number} 秒数
   */
  msToSeconds(milliseconds) {
    return Math.floor(milliseconds / 1000);
  },

  /**
   * 将秒转换为毫秒
   * @param {number} seconds - 秒数
   * @returns {number} 毫秒数
   */
  secondsToMs(seconds) {
    return seconds * 1000;
  },

  /**
   * 获取相对时间描述
   * @param {Date|number} date - 日期对象或时间戳
   * @returns {string} 相对时间描述
   */
  getRelativeTime(date) {
    const now = new Date();
    const target = date instanceof Date ? date : new Date(date);
    const diffMs = now - target;
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSeconds < 60) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return this.formatDateTime(target, 'YYYY-MM-DD');
    }
  }
};
