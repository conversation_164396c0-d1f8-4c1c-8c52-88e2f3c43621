/* ===== 侧边栏布局样式 ===== */

/* 树形结构基础样式 */
.tree-section,
.tree-subsection,
.tree-item {
  position: relative;
  margin-bottom: var(--spacing-xs);
  transition: var(--transition);
}

/* 树形节点头部 */
.tree-node-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-xs);
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.tree-node-header:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

.tree-node-header.selected {
  background: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
  box-shadow: var(--shadow-md);
}

.tree-node-header.completed {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.tree-node-header.selected .node-title {
  color: white !important;
}

/* 树形节点内容 */
.tree-node-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-xs);
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.tree-node-content:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

.tree-node-content.selected {
  background: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
  box-shadow: var(--shadow-md);
}

.tree-node-content.selected .item-title {
  color: white !important;
}

/* 折叠按钮 */
.collapse-btn {
  width: 24px;
  height: 24px;
  margin-right: var(--spacing-sm);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: var(--transition);
  flex-shrink: 0;
}

.collapse-btn:hover {
  background: var(--bg-secondary);
}

.tree-node-header.selected .collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.collapse-icon {
  transition: transform 0.2s ease;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.tree-node-header.selected .collapse-icon {
  color: white;
}

.collapse-btn.collapsed .collapse-icon {
  transform: rotate(-90deg);
}

/* 节点标题 */
.node-title {
  flex: 1;
  margin: 0 var(--spacing-sm) 0 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.item-title {
  flex: 1;
  font-weight: var(--font-weight-medium);
  margin-right: var(--spacing-sm);
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.tree-item.completed .item-title {
  text-decoration: line-through;
  opacity: 0.7;
}

.tree-node-content.selected .item-title {
  text-decoration: none;
  opacity: 1;
}

/* 进度指示器 */
.progress-indicator {
  margin-left: auto;
  margin-right: var(--spacing-sm);
  padding: 2px 6px;
  font-size: var(--font-size-xs);
  background: var(--bg-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  flex-shrink: 0;
}

.tree-node-header.selected .progress-indicator {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

/* 时间显示 */
.time-spent {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  flex-shrink: 0;
}

.tree-node-content.selected .time-spent {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 复选框按钮 */
.btn-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  flex-shrink: 0;
  margin-right: var(--spacing-sm);
  position: relative;
}

.btn-checkbox:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.btn-checkbox.checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.tree-node-header.selected .btn-checkbox {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.1);
}

.tree-node-header.selected .btn-checkbox:hover {
  border-color: white;
  background: rgba(255, 255, 255, 0.2);
}

.tree-node-header.selected .btn-checkbox.checked {
  background: white;
  border-color: white;
}

.checkmark {
  color: white;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
  font-weight: bold;
}

.btn-checkbox.checked .checkmark {
  opacity: 1;
}

.tree-node-header.selected .btn-checkbox.checked .checkmark {
  color: var(--primary-color);
}

/* 计时器按钮 */
.btn-timer {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: var(--transition);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.btn-timer:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

.tree-node-content.selected .btn-timer {
  color: rgba(255, 255, 255, 0.8);
}

.tree-node-content.selected .btn-timer:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 树形子节点容器 */
.tree-children {
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  max-height: 2000px;
  padding-left: var(--spacing-lg);
  margin-left: var(--spacing-sm);
  border-left: 2px solid var(--border-color);
  position: relative;
}

.tree-children.collapsed {
  max-height: 0;
  padding: 0;
  margin: 0;
}

.tree-children::before {
  content: '';
  position: absolute;
  left: -2px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--border-color), transparent);
  opacity: 0.5;
}

/* 项目笔记 */
.item-notes {
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  margin-top: 4px;
  line-height: var(--line-height-relaxed);
}

/* 空状态 */
.sidebar-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  min-height: 300px;
  color: var(--text-muted);
}

.sidebar-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.sidebar-empty h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.sidebar-empty p {
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

/* 侧边栏滚动优化 */
.roadmap-content {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
  /* {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-28 04:00:00 +08:00]
  // Reason: 修复左侧栏Y轴过长问题 - 添加最大高度和滚动
  // Principle_Applied: 用户体验 - 限制内容高度，避免页面过长影响浏览
  // Optimization: 解决用户反馈的左侧栏内容过多时显示问题
  // }} */
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  overflow-x: hidden;
}

.roadmap-content::-webkit-scrollbar {
  width: 6px;
}

.roadmap-content::-webkit-scrollbar-track {
  background: transparent;
}

.roadmap-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.roadmap-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* 响应式侧边栏 */
@media (max-width: 768px) {
  .tree-children {
    padding-left: var(--spacing-md);
    margin-left: var(--spacing-xs);
  }

  .node-title {
    font-size: var(--font-size-base);
  }

  .item-title {
    font-size: var(--font-size-sm);
  }

  .tree-node-header,
  .tree-node-content {
    padding: var(--spacing-xs);
  }

  .collapse-btn {
    width: 20px;
    height: 20px;
  }

  .btn-checkbox {
    width: 18px;
    height: 18px;
  }

  .checkmark {
    font-size: 10px;
  }

  .progress-indicator,
  .time-spent {
    font-size: 10px;
    padding: 1px 4px;
  }
}

@media (max-width: 480px) {
  .tree-node-header,
  .tree-node-content {
    padding: var(--spacing-xs);
    margin-bottom: 2px;
  }

  .tree-children {
    padding-left: var(--spacing-sm);
    border-left-width: 1px;
  }

  .node-title,
  .item-title {
    font-size: var(--font-size-sm);
  }

  .sidebar-empty {
    padding: var(--spacing-lg);
    min-height: 200px;
  }

  .sidebar-empty-icon {
    font-size: 2rem;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .tree-node-header,
  .tree-node-content {
    border-width: 2px;
  }

  .tree-children {
    border-left-width: 3px;
  }

  .btn-checkbox {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .tree-node-header,
  .tree-node-content,
  .collapse-icon,
  .tree-children,
  .checkmark {
    transition: none;
  }

  .tree-node-header:hover,
  .tree-node-content:hover {
    transform: none;
  }
}

/* 打印样式 */
@media print {
  .tree-node-header,
  .tree-node-content {
    box-shadow: none;
    border: 1px solid #000;
    background: white;
    color: black;
  }

  .tree-node-header:hover,
  .tree-node-content:hover {
    transform: none;
    box-shadow: none;
  }

  .btn-timer,
  .btn-checkbox {
    display: none;
  }

  .tree-children {
    border-left-color: #000;
  }
}
