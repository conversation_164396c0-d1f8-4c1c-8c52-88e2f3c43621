/* ===== 头部布局样式 ===== */

/* 基础容器 */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* 头部样式 */
.header {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: var(--z-index-sticky);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  gap: var(--spacing-md);
}

/* Logo区域 */
.logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  margin: 0;
  line-height: var(--line-height-tight);
  flex-shrink: 0;
}

/* 头部控制区域 */
.header-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

/* 快速导航区域 */
.quick-nav {
  margin: var(--spacing-md) 0;
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  background: var(--bg-card);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  flex-wrap: wrap;
}

.quick-nav .search-input {
  flex: 1;
  min-width: 200px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition);
}

.quick-nav .search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.quick-nav .search-input::placeholder {
  color: var(--text-muted);
}

/* 路线图头部 */
.roadmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.roadmap-header h2 {
  font-size: var(--font-size-2xl);
  color: var(--text-primary);
  margin: 0;
  flex-shrink: 0;
}

.roadmap-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* 进度条容器 */
.progress-bar-container {
  min-width: 200px;
  flex-shrink: 0;
}

.progress-bar-container .progress-title {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-bar-container .progress-percentage {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 侧边栏头部 */
.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-card);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
}

.sidebar-header h3 {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

.sidebar-toggle:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.toggle-icon {
  transition: transform 0.2s ease;
  font-size: var(--font-size-sm);
}

/* 内容区头部 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-card);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
}

.content-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.content-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

/* 粘性头部效果 */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
}

.sticky-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
  opacity: 0;
  transition: var(--transition);
}

.sticky-header.scrolled::after {
  opacity: 1;
}

/* 头部动画效果 */
.header-slide-down {
  animation: header-slide-down 0.3s ease;
}

@keyframes header-slide-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.breadcrumb-item:not(:last-child)::after {
  content: '›';
  color: var(--text-muted);
  font-size: var(--font-size-xs);
}

.breadcrumb-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
}

.breadcrumb-link:hover {
  color: var(--primary-color);
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 响应式头部 */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-sm);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .header-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .logo {
    text-align: center;
    font-size: var(--font-size-xl);
  }

  .roadmap-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .roadmap-header h2 {
    text-align: center;
    font-size: var(--font-size-xl);
  }

  .roadmap-controls {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .progress-bar-container {
    min-width: auto;
    width: 100%;
  }

  .quick-nav {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .quick-nav .search-input {
    width: 100%;
    min-width: auto;
  }

  .content-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }

  .content-actions {
    justify-content: center;
  }

  .sidebar-header h3 {
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  .header {
    margin: 0 calc(-1 * var(--spacing-sm));
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .roadmap-header {
    margin: 0 calc(-1 * var(--spacing-sm));
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .quick-nav {
    margin: var(--spacing-sm) calc(-1 * var(--spacing-sm));
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .logo {
    font-size: var(--font-size-lg);
  }

  .roadmap-header h2 {
    font-size: var(--font-size-lg);
  }

  .content-header h3 {
    font-size: var(--font-size-base);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .header,
  .roadmap-header,
  .quick-nav,
  .sidebar-header,
  .content-header {
    border-width: 2px;
  }
}

/* 打印样式 */
@media print {
  .header-controls,
  .roadmap-controls,
  .content-actions,
  .sidebar-toggle {
    display: none;
  }

  .header,
  .roadmap-header,
  .quick-nav {
    box-shadow: none;
    border: 1px solid #000;
  }

  .logo {
    color: #000;
  }
}
