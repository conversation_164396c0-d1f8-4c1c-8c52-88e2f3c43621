/* ===== 字体排版样式 ===== */

/* 基础字体类 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

/* 字体粗细类 */
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 行高类 */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* 文本颜色类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

/* 文本对齐类 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 文本装饰类 */
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

/* 文本转换类 */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* 字体样式类 */
.italic { font-style: italic; }
.not-italic { font-style: normal; }

/* 字体族类 */
.font-mono { font-family: var(--font-family-mono); }
.font-sans { font-family: var(--font-family-base); }

/* 文本溢出处理 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

/* 换行控制 */
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

.break-normal { 
  overflow-wrap: normal; 
  word-break: normal; 
}

.break-words { 
  overflow-wrap: break-word; 
}

.break-all { 
  word-break: break-all; 
}

/* 特殊文本样式 */
.logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  margin: 0;
  line-height: var(--line-height-tight);
}

.heading-primary {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-lg);
}

.heading-secondary {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
}

.heading-tertiary {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-sm);
}

.body-large {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
}

.body-normal {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
}

.body-small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
}

.caption {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-normal);
  color: var(--text-muted);
}

/* 代码文本样式 */
.code-inline {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  background-color: var(--bg-secondary);
  padding: 2px 4px;
  border-radius: 4px;
  color: var(--text-primary);
}

.code-block {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  overflow-x: auto;
  border: 1px solid var(--border-color);
  line-height: var(--line-height-relaxed);
}

/* 链接样式变体 */
.link-primary {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

.link-primary:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.link-secondary {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
}

.link-secondary:hover {
  color: var(--text-primary);
  text-decoration: underline;
}

.link-muted {
  color: var(--text-muted);
  text-decoration: none;
  transition: var(--transition);
}

.link-muted:hover {
  color: var(--text-secondary);
  text-decoration: underline;
}

/* 列表样式 */
.list-disc {
  list-style-type: disc;
  padding-left: var(--spacing-lg);
}

.list-decimal {
  list-style-type: decimal;
  padding-left: var(--spacing-lg);
}

.list-none {
  list-style: none;
  padding-left: 0;
}

.list-inside {
  list-style-position: inside;
}

.list-outside {
  list-style-position: outside;
}

/* 引用样式 */
.blockquote {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
  background-color: var(--bg-secondary);
  font-style: italic;
  color: var(--text-secondary);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.blockquote-primary {
  border-left-color: var(--primary-color);
}

.blockquote-success {
  border-left-color: var(--success-color);
}

.blockquote-warning {
  border-left-color: var(--warning-color);
}

.blockquote-error {
  border-left-color: var(--error-color);
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  .heading-primary {
    font-size: var(--font-size-2xl);
  }
  
  .heading-secondary {
    font-size: var(--font-size-xl);
  }
  
  .heading-tertiary {
    font-size: var(--font-size-lg);
  }
  
  .logo {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .heading-primary {
    font-size: var(--font-size-xl);
  }
  
  .heading-secondary {
    font-size: var(--font-size-lg);
  }
  
  .heading-tertiary {
    font-size: var(--font-size-base);
  }
  
  .logo {
    font-size: var(--font-size-lg);
  }
}

/* 打印样式 */
@media print {
  .heading-primary,
  .heading-secondary,
  .heading-tertiary {
    color: black;
    page-break-after: avoid;
  }
  
  .blockquote {
    border: 1px solid #999;
    background: none;
    page-break-inside: avoid;
  }
}
