/* ===== 表单组件样式 ===== */

/* 基础表单样式 */
.form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-end;
}

.form-row .form-group {
  flex: 1;
}

/* 标签样式 */
.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

/* 输入框基础样式 */
.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: var(--transition);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
  background-color: var(--bg-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-muted);
}

/* 输入框尺寸变体 */
.form-input-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.form-input-lg {
  padding: var(--spacing-md);
  font-size: var(--font-size-lg);
}

/* 文本域样式 */
.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-textarea.no-resize {
  resize: none;
}

/* 搜索输入框 */
.search-input {
  position: relative;
  padding-left: var(--spacing-xl);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: var(--spacing-sm) center;
  background-size: 16px 16px;
}

.search-input-with-icon {
  padding-left: var(--spacing-xl);
}

.search-input.search-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-input.has-search-term {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
}

/* 选择框样式 */
.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right var(--spacing-sm) center;
  background-size: 16px 16px;
  padding-right: var(--spacing-xl);
  cursor: pointer;
}

/* 复选框和单选框 */
.form-checkbox,
.form-radio {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  flex-shrink: 0;
}

.form-checkbox {
  border-radius: 4px;
}

.form-radio {
  border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.form-radio:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
}

.form-checkbox:focus,
.form-radio:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 复选框/单选框标签组合 */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.form-check input {
  margin: 0;
}

.form-check label {
  margin: 0;
  cursor: pointer;
  user-select: none;
}

/* 文件输入框 */
.form-file {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.form-file input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.form-file-label {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
}

.form-file:hover .form-file-label {
  background: var(--border-color);
  border-color: var(--border-hover);
}

/* 表单验证状态 */
.form-group.has-error .form-input,
.form-group.has-error .form-textarea,
.form-group.has-error .form-select {
  border-color: var(--error-color);
}

.form-group.has-error .form-input:focus,
.form-group.has-error .form-textarea:focus,
.form-group.has-error .form-select:focus {
  box-shadow: 0 0 0 3px var(--error-light);
}

.form-group.has-success .form-input,
.form-group.has-success .form-textarea,
.form-group.has-success .form-select {
  border-color: var(--success-color);
}

.form-group.has-success .form-input:focus,
.form-group.has-success .form-textarea:focus,
.form-group.has-success .form-select:focus {
  box-shadow: 0 0 0 3px var(--success-light);
}

/* 表单帮助文本 */
.form-help {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--error-color);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form-error::before {
  content: '⚠';
  font-size: var(--font-size-sm);
}

/* 会话笔记样式 */
.session-notes {
  margin-top: var(--spacing-md);
}

.session-notes textarea {
  width: 100%;
  height: 100px;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
  margin-bottom: var(--spacing-sm);
  transition: var(--transition);
}

.session-notes textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.session-notes textarea::placeholder {
  color: var(--text-muted);
}

/* 输入组 */
.input-group {
  display: flex;
  align-items: stretch;
}

.input-group .form-input {
  border-radius: 0;
  border-right: none;
}

.input-group .form-input:first-child {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

.input-group .form-input:last-child {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  border-right: 1px solid var(--border-color);
}

.input-group-addon {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.input-group-addon:first-child {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
  border-right: none;
}

.input-group-addon:last-child {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  border-left: none;
}

/* 响应式表单 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .search-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  .form-input,
  .form-textarea,
  .form-select {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .form-input,
  .form-textarea,
  .form-select {
    border-width: 2px;
  }
  
  .form-input:focus,
  .form-textarea:focus,
  .form-select:focus {
    border-color: #000000;
    box-shadow: 0 0 0 3px #000000;
  }
}

/* 打印样式 */
@media print {
  .form-input,
  .form-textarea,
  .form-select {
    border: 1px solid #000;
    background: transparent;
  }
  
  .form-file,
  .upload-area {
    display: none;
  }
}
