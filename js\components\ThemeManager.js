/**
 * 主题管理组件
 * 负责深色/浅色主题切换
 */
class ThemeManager {
  constructor(eventManager) {
    this.eventManager = eventManager;
    this.currentTheme = AppConstants.CONFIG.THEMES.LIGHT;
    
    // DOM元素引用
    this.elements = {
      darkModeToggle: DOMUtils.$(AppConstants.SELECTORS.DARK_MODE_TOGGLE),
      themeIcon: null // 将在初始化时设置
    };

    this.init();
  }

  /**
   * 初始化主题管理组件
   */
  init() {
    this.loadSavedTheme();
    this.bindEvents();
    this.updateThemeIcon();
    this.setupThemeIcon();
  }

  /**
   * 设置主题图标元素引用
   */
  setupThemeIcon() {
    if (this.elements.darkModeToggle) {
      this.elements.themeIcon = DOMUtils.$('.icon', this.elements.darkModeToggle);
    }
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 绑定主题切换按钮事件
    if (this.elements.darkModeToggle) {
      DOMUtils.on(this.elements.darkModeToggle, 'click', () => {
        this.toggleTheme();
      });
    }

    // 监听系统主题变化（如果支持）
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      // 监听系统主题变化
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', (e) => {
          this.handleSystemThemeChange(e);
        });
      } else if (mediaQuery.addListener) {
        // 兼容旧版浏览器
        mediaQuery.addListener((e) => {
          this.handleSystemThemeChange(e);
        });
      }
    }

    // 键盘快捷键支持
    DOMUtils.on(document, 'keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });
  }

  /**
   * 加载保存的主题设置
   */
  loadSavedTheme() {
    const savedTheme = StorageUtils.getLocal(
      AppConstants.CONFIG.STORAGE_KEYS.THEME, 
      this.getSystemPreferredTheme()
    );
    
    if (this.isValidTheme(savedTheme)) {
      this.setTheme(savedTheme, false); // 不保存，因为是从存储加载的
    } else {
      // 如果保存的主题无效，使用系统偏好
      this.setTheme(this.getSystemPreferredTheme());
    }
  }

  /**
   * 获取系统偏好的主题
   * @returns {string} 主题名称
   */
  getSystemPreferredTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return AppConstants.CONFIG.THEMES.DARK;
    }
    return AppConstants.CONFIG.THEMES.LIGHT;
  }

  /**
   * 验证主题是否有效
   * @param {string} theme - 主题名称
   * @returns {boolean} 是否有效
   */
  isValidTheme(theme) {
    return Object.values(AppConstants.CONFIG.THEMES).includes(theme);
  }

  /**
   * 切换主题
   */
  toggleTheme() {
    const newTheme = this.currentTheme === AppConstants.CONFIG.THEMES.DARK 
      ? AppConstants.CONFIG.THEMES.LIGHT 
      : AppConstants.CONFIG.THEMES.DARK;
    
    this.setTheme(newTheme);
    
    // 添加切换动画效果
    this.addToggleAnimation();
  }

  /**
   * 设置主题
   * @param {string} theme - 主题名称
   * @param {boolean} save - 是否保存到本地存储
   */
  setTheme(theme, save = true) {
    if (!this.isValidTheme(theme)) {
      console.warn('无效的主题:', theme);
      return;
    }

    const previousTheme = this.currentTheme;
    this.currentTheme = theme;

    // 应用主题到DOM
    this.applyThemeToDOM(theme);

    // 更新图标
    this.updateThemeIcon();

    // 保存到本地存储
    if (save) {
      this.saveTheme(theme);
    }

    // 触发主题变化事件
    this.eventManager.emit(AppConstants.EVENTS.THEME_CHANGED, {
      previousTheme,
      currentTheme: theme,
      timestamp: Date.now()
    });

    console.log(`主题已切换到: ${theme}`);
  }

  /**
   * 应用主题到DOM
   * @param {string} theme - 主题名称
   */
  applyThemeToDOM(theme) {
    // 设置根元素的data-theme属性
    document.documentElement.setAttribute('data-theme', theme);

    // 添加主题切换的过渡效果
    document.documentElement.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    
    // 移除过渡效果（避免影响其他动画）
    setTimeout(() => {
      document.documentElement.style.transition = '';
    }, 300);
  }

  /**
   * 更新主题图标
   */
  updateThemeIcon() {
    if (!this.elements.themeIcon) return;

    const icon = this.currentTheme === AppConstants.CONFIG.THEMES.DARK 
      ? AppConstants.ICONS.SUN 
      : AppConstants.ICONS.MOON;
    
    DOMUtils.setContent(this.elements.themeIcon, icon);

    // 更新按钮标题
    if (this.elements.darkModeToggle) {
      const title = this.currentTheme === AppConstants.CONFIG.THEMES.DARK 
        ? '切换到浅色模式' 
        : '切换到深色模式';
      
      DOMUtils.setAttr(this.elements.darkModeToggle, 'title', title);
    }
  }

  /**
   * 添加切换动画效果
   */
  addToggleAnimation() {
    if (!this.elements.darkModeToggle) return;

    // 添加动画类
    DOMUtils.addClass(this.elements.darkModeToggle, 'theme-switching');

    // 移除动画类
    setTimeout(() => {
      DOMUtils.removeClass(this.elements.darkModeToggle, 'theme-switching');
    }, 300);
  }

  /**
   * 保存主题到本地存储
   * @param {string} theme - 主题名称
   */
  saveTheme(theme) {
    const success = StorageUtils.setLocal(AppConstants.CONFIG.STORAGE_KEYS.THEME, theme);
    
    if (!success) {
      console.warn('主题保存失败');
    }
  }

  /**
   * 处理系统主题变化
   * @param {MediaQueryListEvent} event - 媒体查询事件
   */
  handleSystemThemeChange(event) {
    // 只有在用户没有手动设置主题时才跟随系统
    const hasManualTheme = StorageUtils.hasLocal(AppConstants.CONFIG.STORAGE_KEYS.THEME);
    
    if (!hasManualTheme) {
      const systemTheme = event.matches 
        ? AppConstants.CONFIG.THEMES.DARK 
        : AppConstants.CONFIG.THEMES.LIGHT;
      
      this.setTheme(systemTheme);
    }
  }

  /**
   * 处理键盘快捷键
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + Shift + T: 切换主题
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.code === 'KeyT') {
      event.preventDefault();
      this.toggleTheme();
    }
  }

  /**
   * 获取当前主题
   * @returns {string} 当前主题名称
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 检查是否为深色主题
   * @returns {boolean} 是否为深色主题
   */
  isDarkTheme() {
    return this.currentTheme === AppConstants.CONFIG.THEMES.DARK;
  }

  /**
   * 检查是否为浅色主题
   * @returns {boolean} 是否为浅色主题
   */
  isLightTheme() {
    return this.currentTheme === AppConstants.CONFIG.THEMES.LIGHT;
  }

  /**
   * 强制设置为深色主题
   */
  setDarkTheme() {
    this.setTheme(AppConstants.CONFIG.THEMES.DARK);
  }

  /**
   * 强制设置为浅色主题
   */
  setLightTheme() {
    this.setTheme(AppConstants.CONFIG.THEMES.LIGHT);
  }

  /**
   * 重置为系统偏好主题
   */
  resetToSystemTheme() {
    const systemTheme = this.getSystemPreferredTheme();
    this.setTheme(systemTheme);
    
    // 清除手动设置的主题
    StorageUtils.removeLocal(AppConstants.CONFIG.STORAGE_KEYS.THEME);
  }

  /**
   * 获取主题信息
   * @returns {Object} 主题信息对象
   */
  getThemeInfo() {
    return {
      currentTheme: this.currentTheme,
      isDark: this.isDarkTheme(),
      isLight: this.isLightTheme(),
      systemPreferred: this.getSystemPreferredTheme(),
      availableThemes: Object.values(AppConstants.CONFIG.THEMES),
      hasManualSetting: StorageUtils.hasLocal(AppConstants.CONFIG.STORAGE_KEYS.THEME)
    };
  }

  /**
   * 导出主题设置
   * @returns {Object} 主题设置对象
   */
  exportThemeSettings() {
    return {
      currentTheme: this.currentTheme,
      savedTheme: StorageUtils.getLocal(AppConstants.CONFIG.STORAGE_KEYS.THEME),
      systemTheme: this.getSystemPreferredTheme(),
      exportTime: Date.now()
    };
  }

  /**
   * 导入主题设置
   * @param {Object} settings - 主题设置对象
   * @returns {boolean} 是否导入成功
   */
  importThemeSettings(settings) {
    if (settings && this.isValidTheme(settings.currentTheme)) {
      this.setTheme(settings.currentTheme);
      return true;
    }
    return false;
  }

  /**
   * 销毁组件
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.THEME_CHANGED);
    
    // 清理数据
    this.currentTheme = null;
    this.elements = {};
  }
}

// 导出组件
window.ThemeManager = ThemeManager;
