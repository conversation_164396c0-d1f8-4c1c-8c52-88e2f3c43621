/* ===== 内容详情组件样式 ===== */

/* 项目详情容器 */
.item-detail {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
}

/* 详情头部 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.item-type-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.item-type-badge.section {
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.item-type-badge.subsection {
  background: var(--info-light);
  color: var(--info-color);
  border: 1px solid var(--info-color);
}

.item-type-badge.item {
  background: var(--success-light);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.item-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.item-status.completed {
  background: var(--success-light);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.item-status.pending {
  background: var(--warning-light);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

/* 详情内容 */
.detail-content {
  padding: var(--spacing-md);
}

.detail-content h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

/* 统计信息 */
.detail-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

/* 子项目概览 */
.children-overview {
  margin-bottom: var(--spacing-lg);
}

.children-overview h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.children-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.child-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  cursor: pointer;
}

.child-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

.child-item.completed {
  opacity: 0.7;
}

.child-item.completed .child-title {
  text-decoration: line-through;
}

.child-status {
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.child-title {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.child-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: var(--bg-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: var(--font-weight-medium);
  flex-shrink: 0;
}

/* 深层级内容树 */
.deep-content-tree {
  margin-bottom: var(--spacing-lg);
}

.deep-content-tree h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.deep-tree-container {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm);
  max-height: 400px;
  overflow-y: auto;
}

.deep-tree-container::-webkit-scrollbar {
  width: 6px;
}

.deep-tree-container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.deep-tree-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.deep-tree-container::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

.deep-tree-item {
  margin-bottom: var(--spacing-xs);
}

.deep-item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.deep-item-content:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

.deep-item-content.completed {
  opacity: 0.7;
  background: var(--success-light);
}

.deep-item-content.completed .deep-item-title {
  text-decoration: line-through;
}

/* 迷你控制按钮 */
.mini-collapse-btn {
  width: 16px;
  height: 16px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: var(--transition);
  flex-shrink: 0;
}

.mini-collapse-btn:hover {
  background: var(--bg-secondary);
}

.mini-collapse-icon {
  font-size: 10px;
  color: var(--text-secondary);
  transition: transform 0.2s ease;
}

.mini-collapse-btn.collapsed .mini-collapse-icon {
  transform: rotate(-90deg);
}

.mini-spacer {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.mini-checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: 3px;
  background: var(--bg-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  flex-shrink: 0;
}

.mini-checkbox:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.mini-checkbox.checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.mini-checkmark {
  color: white;
  font-size: 10px;
  opacity: 0;
  transition: opacity 0.2s ease;
  font-weight: bold;
}

.mini-checkbox.checked .mini-checkmark {
  opacity: 1;
}

.deep-item-type-icon {
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.deep-item-title {
  flex: 1;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.deep-item-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: var(--font-weight-medium);
  flex-shrink: 0;
}

.mini-timer {
  background: none;
  border: none;
  font-size: var(--font-size-sm);
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: var(--transition);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.mini-timer:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

.deep-children {
  margin-top: var(--spacing-xs);
  padding-left: var(--spacing-md);
  border-left: 1px solid var(--border-color);
  /* {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-28 04:10:00 +08:00]
  // Reason: 修复右侧栏深层级折叠功能 - 添加折叠状态样式
  // Principle_Applied: 用户体验 - 确保折叠按钮能够真正隐藏子内容
  // Optimization: 解决用户反馈的右侧栏折叠按钮不生效问题
  // }} */
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  max-height: 1000px;
  opacity: 1;
}

.deep-children.collapsed {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
  padding-left: 0;
}

/* 笔记显示 */
.item-notes-display {
  margin-bottom: var(--spacing-lg);
}

.item-notes-display h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.notes-content {
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  white-space: pre-wrap;
}
