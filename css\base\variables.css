/* ===== CSS变量定义 ===== */
:root {
  /* 主色调 */
  --primary-color: #3B82F6;
  --primary-hover: #2563EB;
  --primary-light: #DBEAFE;

  /* 背景颜色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F3F4F6;
  --bg-card: #FFFFFF;

  /* 文字颜色 */
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-muted: #9CA3AF;

  /* 边框和分割线 */
  --border-color: #E5E7EB;
  --border-hover: #D1D5DB;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 圆角 */
  --border-radius: 8px;
  --border-radius-lg: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 动画 */
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease;
  --transition-slow: all 0.3s ease;

  /* 字体 */
  --font-family-base: 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'Courier New', 'Monaco', 'Menlo', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 2;

  /* Z-index层级 */
  --z-index-dropdown: 100;
  --z-index-sticky: 200;
  --z-index-fixed: 300;
  --z-index-modal-backdrop: 999;
  --z-index-modal: 1000;
  --z-index-popover: 1100;
  --z-index-tooltip: 1200;

  /* 断点 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 容器最大宽度 */
  --container-max-width: 1200px;

  /* 进度条颜色 */
  --progress-low: #EF4444;
  --progress-medium: #F59E0B;
  --progress-high: #10B981;
  --progress-complete: #059669;

  /* 状态颜色 */
  --success-color: #10B981;
  --success-light: #D1FAE5;
  --warning-color: #F59E0B;
  --warning-light: #FEF3C7;
  --error-color: #EF4444;
  --error-light: #FEE2E2;
  --info-color: #3B82F6;
  --info-light: #DBEAFE;
}

/* 深色模式变量 */
[data-theme="dark"] {
  /* 背景颜色 */
  --bg-primary: #111827;
  --bg-secondary: #1F2937;
  --bg-card: #1F2937;

  /* 文字颜色 */
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --text-muted: #9CA3AF;

  /* 边框和分割线 */
  --border-color: #374151;
  --border-hover: #4B5563;

  /* 状态颜色（深色模式调整） */
  --success-light: #064E3B;
  --warning-light: #78350F;
  --error-light: #7F1D1D;
  --info-light: #1E3A8A;
  --primary-light: #1E3A8A;

  /* 进度条颜色（深色模式调整） */
  --progress-low: #DC2626;
  --progress-medium: #D97706;
  --progress-high: #059669;
  --progress-complete: #047857;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }

  [data-theme="dark"] {
    --border-color: #FFFFFF;
    --text-secondary: #FFFFFF;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition: none;
    --transition-fast: none;
    --transition-slow: none;
  }
}

/* 打印模式变量 */
@media print {
  :root {
    --bg-primary: #FFFFFF;
    --bg-secondary: #FFFFFF;
    --bg-card: #FFFFFF;
    --text-primary: #000000;
    --text-secondary: #000000;
    --border-color: #000000;
    --shadow-sm: none;
    --shadow-md: none;
    --shadow-lg: none;
  }
}
