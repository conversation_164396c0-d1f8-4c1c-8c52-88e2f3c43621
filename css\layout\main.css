/* ===== 主要内容区布局样式 ===== */

/* 主要布局容器 */
.main-layout {
  min-height: 100vh;
  background: var(--bg-secondary);
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 统计面板布局 */
.stats-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stats-panel .stat-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stats-panel .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 左右分栏布局 */
.roadmap-container {
  display: flex;
  gap: var(--spacing-md);
  min-height: 600px;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  overflow: hidden;
  flex: 1;
}

/* 左侧导航栏 */
.roadmap-sidebar {
  width: 30%;
  min-width: 250px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: var(--transition);
  position: relative;
}

.roadmap-sidebar.collapsed {
  width: 60px;
  min-width: 60px;
}

.roadmap-sidebar.collapsed .sidebar-header h3,
.roadmap-sidebar.collapsed .roadmap-content {
  opacity: 0;
  pointer-events: none;
}

.roadmap-sidebar.collapsed .toggle-icon {
  transform: rotate(180deg);
}

.roadmap-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
  transition: var(--transition);
}

.roadmap-content::-webkit-scrollbar {
  width: 6px;
}

.roadmap-content::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.roadmap-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.roadmap-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* 右侧内容展示区 */
.content-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  min-width: 0; /* 防止flex子项溢出 */
}

.content-body {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  position: relative;
}

.content-body::-webkit-scrollbar {
  width: 8px;
}

.content-body::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.content-body::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.content-body::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* 欢迎页样式 */
.welcome-content {
  text-align: center;
  padding: var(--spacing-xl);
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.6;
  transition: var(--transition);
}

.welcome-content:hover .welcome-icon {
  opacity: 0.8;
  transform: scale(1.05);
}

.welcome-content h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-2xl);
}

.welcome-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: var(--line-height-relaxed);
}

/* 功能卡片网格 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.feature-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  text-align: center;
  transition: var(--transition);
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
  opacity: 0.8;
  transition: var(--transition);
}

.feature-card:hover .feature-icon {
  opacity: 1;
  transform: scale(1.1);
}

.feature-card h4 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.feature-card p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

/* 内容区域状态 */
.content-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.content-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  margin-right: var(--spacing-sm);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.content-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--error-color);
  text-align: center;
  padding: var(--spacing-lg);
}

.content-error::before {
  content: '⚠️';
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

/* 分割器 */
.divider {
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-lg) 0;
  border: none;
}

.divider-vertical {
  width: 1px;
  background: var(--border-color);
  margin: 0 var(--spacing-md);
  border: none;
  align-self: stretch;
}

/* 可调整大小的分割器 */
.resizable-divider {
  width: 4px;
  background: var(--border-color);
  cursor: col-resize;
  transition: var(--transition);
  position: relative;
}

.resizable-divider:hover {
  background: var(--primary-color);
}

.resizable-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 20px;
  background: var(--bg-primary);
  border-radius: 1px;
}

/* 网格布局工具类 */
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Flex布局工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* 间距工具类 */
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* 响应式布局 */
@media (max-width: 768px) {
  .roadmap-container {
    flex-direction: column;
    min-height: auto;
  }

  .roadmap-sidebar {
    width: 100%;
    min-width: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    max-height: 300px;
  }

  .roadmap-sidebar.collapsed {
    width: 100%;
    max-height: 60px;
  }

  .stats-panel {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
  }

  .feature-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }

  .welcome-content {
    padding: var(--spacing-lg);
  }

  .welcome-icon {
    font-size: 3rem;
  }

  .welcome-content h2 {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .roadmap-container {
    margin: 0 calc(-1 * var(--spacing-sm));
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .stats-panel {
    grid-template-columns: 1fr;
    margin: 0 calc(-1 * var(--spacing-sm));
  }

  .stats-panel .stat-card {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .feature-cards {
    grid-template-columns: 1fr;
  }

  .welcome-content {
    padding: var(--spacing-md);
  }

  .welcome-icon {
    font-size: 2.5rem;
  }
}

/* 打印样式 */
@media print {
  .main-layout {
    background: white;
  }

  .roadmap-container {
    box-shadow: none;
    border: 1px solid #000;
    flex-direction: column;
  }

  .roadmap-sidebar {
    border-right: none;
    border-bottom: 1px solid #000;
  }

  .content-loading,
  .content-error {
    display: none;
  }
}
