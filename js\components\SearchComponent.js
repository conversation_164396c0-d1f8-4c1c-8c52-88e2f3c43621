/**
 * 搜索组件
 * 负责搜索过滤、结果高亮
 */
class SearchComponent {
  constructor(eventManager) {
    this.eventManager = eventManager;
    this.searchTerm = '';
    this.searchHistory = [];
    this.maxHistoryItems = 10;
    
    // DOM元素引用
    this.elements = {
      searchInput: DOMUtils.$(AppConstants.SELECTORS.SEARCH_INPUT),
      expandAllBtn: DOMUtils.$(AppConstants.SELECTORS.EXPAND_ALL_BTN),
      collapseAllBtn: DOMUtils.$(AppConstants.SELECTORS.COLLAPSE_ALL_BTN),
      sidebarToggle: DOMUtils.$(AppConstants.SELECTORS.SIDEBAR_TOGGLE)
    };

    // 防抖搜索函数
    this.debouncedSearch = DOMUtils.debounce(
      (searchTerm) => this.performSearch(searchTerm),
      AppConstants.CONFIG.SEARCH_DEBOUNCE_DELAY
    );

    this.init();
  }

  /**
   * 初始化搜索组件
   */
  init() {
    this.loadSearchHistory();
    this.bindEvents();
    this.setupSearchInput();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 搜索输入事件
    if (this.elements.searchInput) {
      DOMUtils.on(this.elements.searchInput, 'input', (e) => {
        this.handleSearchInput(e.target.value);
      });

      DOMUtils.on(this.elements.searchInput, 'keydown', (e) => {
        this.handleSearchKeydown(e);
      });

      DOMUtils.on(this.elements.searchInput, 'focus', () => {
        this.handleSearchFocus();
      });

      DOMUtils.on(this.elements.searchInput, 'blur', () => {
        this.handleSearchBlur();
      });
    }

    // 展开/折叠按钮事件
    if (this.elements.expandAllBtn) {
      DOMUtils.on(this.elements.expandAllBtn, 'click', () => {
        this.expandAll();
      });
    }

    if (this.elements.collapseAllBtn) {
      DOMUtils.on(this.elements.collapseAllBtn, 'click', () => {
        this.collapseAll();
      });
    }

    // 侧边栏切换按钮事件
    if (this.elements.sidebarToggle) {
      DOMUtils.on(this.elements.sidebarToggle, 'click', () => {
        this.toggleSidebar();
      });
    }

    // 全局键盘快捷键
    DOMUtils.on(document, 'keydown', (e) => {
      this.handleGlobalKeyboardShortcuts(e);
    });

    // 监听数据清空事件
    this.eventManager.on(AppConstants.EVENTS.DATA_CLEARED, () => {
      this.clearSearch();
    });
  }

  /**
   * 设置搜索输入框
   */
  setupSearchInput() {
    if (!this.elements.searchInput) return;

    // 设置占位符
    DOMUtils.setAttr(this.elements.searchInput, 'placeholder', `${AppConstants.ICONS.SEARCH} 搜索课程内容...`);
    
    // 添加搜索图标样式类
    DOMUtils.addClass(this.elements.searchInput, 'search-input-with-icon');
  }

  /**
   * 处理搜索输入
   * @param {string} value - 输入值
   */
  handleSearchInput(value) {
    const trimmedValue = value.trim();
    
    // 使用防抖搜索
    this.debouncedSearch(trimmedValue);
  }

  /**
   * 执行搜索
   * @param {string} searchTerm - 搜索词
   */
  performSearch(searchTerm) {
    const previousTerm = this.searchTerm;
    this.searchTerm = searchTerm.toLowerCase();

    // 如果搜索词没有变化，不执行搜索
    if (this.searchTerm === previousTerm) {
      return;
    }

    if (this.searchTerm) {
      // 添加到搜索历史
      this.addToSearchHistory(searchTerm);
      
      // 触发搜索执行事件
      this.eventManager.emit(AppConstants.EVENTS.SEARCH_PERFORMED, {
        searchTerm: this.searchTerm,
        originalTerm: searchTerm
      });

      console.log('执行搜索:', searchTerm);
    } else {
      // 清空搜索
      this.eventManager.emit(AppConstants.EVENTS.SEARCH_CLEARED);
      console.log('清空搜索');
    }

    // 更新搜索输入框样式
    this.updateSearchInputStyle();
  }

  /**
   * 处理搜索框键盘事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleSearchKeydown(event) {
    switch (event.code) {
      case 'Escape':
        // ESC键清空搜索
        event.preventDefault();
        this.clearSearch();
        break;
        
      case 'Enter':
        // 回车键执行搜索（立即执行，不等防抖）
        event.preventDefault();
        this.performSearch(event.target.value.trim());
        break;
        
      case 'ArrowDown':
        // 下箭头键显示搜索历史（如果实现了的话）
        event.preventDefault();
        this.showSearchHistory();
        break;
    }
  }

  /**
   * 处理搜索框获得焦点
   */
  handleSearchFocus() {
    DOMUtils.addClass(this.elements.searchInput, 'search-focused');
  }

  /**
   * 处理搜索框失去焦点
   */
  handleSearchBlur() {
    DOMUtils.removeClass(this.elements.searchInput, 'search-focused');
    // 延迟隐藏搜索历史，给用户时间点击
    setTimeout(() => {
      this.hideSearchHistory();
    }, 200);
  }

  /**
   * 更新搜索输入框样式
   */
  updateSearchInputStyle() {
    if (!this.elements.searchInput) return;

    if (this.searchTerm) {
      DOMUtils.addClass(this.elements.searchInput, 'has-search-term');
    } else {
      DOMUtils.removeClass(this.elements.searchInput, 'has-search-term');
    }
  }

  /**
   * 清空搜索
   */
  clearSearch() {
    this.searchTerm = '';
    
    if (this.elements.searchInput) {
      this.elements.searchInput.value = '';
    }
    
    this.updateSearchInputStyle();
    
    // 触发搜索清空事件
    this.eventManager.emit(AppConstants.EVENTS.SEARCH_CLEARED);
    
    console.log('搜索已清空');
  }

  /**
   * 展开所有项目
   */
  expandAll() {
    this.eventManager.emit('items:expand_all');
    console.log('展开所有项目');
  }

  /**
   * 折叠所有项目
   */
  collapseAll() {
    this.eventManager.emit('items:collapse_all');
    console.log('折叠所有项目');
  }

  /**
   * 切换侧边栏
   */
  toggleSidebar() {
    const sidebar = DOMUtils.$('.roadmap-sidebar');
    if (sidebar) {
      const isCollapsed = DOMUtils.toggleClass(sidebar, 'collapsed');
      
      // 更新切换按钮图标
      if (this.elements.sidebarToggle) {
        const toggleIcon = DOMUtils.$('.toggle-icon', this.elements.sidebarToggle);
        if (toggleIcon) {
          DOMUtils.setContent(toggleIcon, isCollapsed ? '▶' : '◀');
        }
      }

      // 触发侧边栏切换事件
      this.eventManager.emit(AppConstants.EVENTS.SIDEBAR_TOGGLED, {
        collapsed: isCollapsed
      });

      console.log('侧边栏已', isCollapsed ? '折叠' : '展开');
    }
  }

  /**
   * 添加到搜索历史
   * @param {string} searchTerm - 搜索词
   */
  addToSearchHistory(searchTerm) {
    if (!searchTerm || searchTerm.length < 2) return;

    // 移除重复项
    this.searchHistory = this.searchHistory.filter(item => item !== searchTerm);
    
    // 添加到开头
    this.searchHistory.unshift(searchTerm);
    
    // 限制历史记录数量
    if (this.searchHistory.length > this.maxHistoryItems) {
      this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
    }

    // 保存到本地存储
    this.saveSearchHistory();
  }

  /**
   * 显示搜索历史
   */
  showSearchHistory() {
    if (this.searchHistory.length === 0) return;

    // 这里可以实现搜索历史下拉菜单
    console.log('搜索历史:', this.searchHistory);
  }

  /**
   * 隐藏搜索历史
   */
  hideSearchHistory() {
    // 隐藏搜索历史下拉菜单
  }

  /**
   * 保存搜索历史到本地存储
   */
  saveSearchHistory() {
    StorageUtils.setLocal('searchHistory', this.searchHistory);
  }

  /**
   * 从本地存储加载搜索历史
   */
  loadSearchHistory() {
    this.searchHistory = StorageUtils.getLocal('searchHistory', []);
  }

  /**
   * 清空搜索历史
   */
  clearSearchHistory() {
    this.searchHistory = [];
    this.saveSearchHistory();
  }

  /**
   * 处理全局键盘快捷键
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleGlobalKeyboardShortcuts(event) {
    // Ctrl/Cmd + F: 聚焦搜索框
    if ((event.ctrlKey || event.metaKey) && event.code === 'KeyF') {
      event.preventDefault();
      this.focusSearchInput();
    }

    // Ctrl/Cmd + K: 聚焦搜索框（现代应用常用快捷键）
    if ((event.ctrlKey || event.metaKey) && event.code === 'KeyK') {
      event.preventDefault();
      this.focusSearchInput();
    }

    // Ctrl/Cmd + E: 展开所有
    if ((event.ctrlKey || event.metaKey) && event.code === 'KeyE') {
      event.preventDefault();
      this.expandAll();
    }

    // Ctrl/Cmd + R: 折叠所有
    if ((event.ctrlKey || event.metaKey) && event.code === 'KeyR') {
      event.preventDefault();
      this.collapseAll();
    }
  }

  /**
   * 聚焦搜索输入框
   */
  focusSearchInput() {
    if (this.elements.searchInput) {
      this.elements.searchInput.focus();
      this.elements.searchInput.select();
    }
  }

  /**
   * 获取当前搜索状态
   * @returns {Object} 搜索状态对象
   */
  getSearchStatus() {
    return {
      searchTerm: this.searchTerm,
      hasActiveSearch: !!this.searchTerm,
      searchHistory: [...this.searchHistory],
      historyCount: this.searchHistory.length
    };
  }

  /**
   * 设置搜索词（程序化搜索）
   * @param {string} searchTerm - 搜索词
   */
  setSearchTerm(searchTerm) {
    if (this.elements.searchInput) {
      this.elements.searchInput.value = searchTerm;
    }
    this.performSearch(searchTerm);
  }

  /**
   * 获取搜索建议（基于历史记录）
   * @param {string} input - 输入内容
   * @returns {Array} 搜索建议数组
   */
  getSearchSuggestions(input) {
    if (!input || input.length < 1) return [];

    const lowerInput = input.toLowerCase();
    return this.searchHistory.filter(item => 
      item.toLowerCase().includes(lowerInput)
    ).slice(0, 5);
  }

  /**
   * 销毁组件
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_CLEARED);
    
    // 清理数据
    this.searchTerm = '';
    this.searchHistory = [];
    this.elements = {};
  }
}

// 导出组件
window.SearchComponent = SearchComponent;
