/**
 * 事件管理器 - 提供发布-订阅模式的事件系统
 * 用于模块间的解耦通信
 */
class EventManager {
  constructor() {
    // 存储事件监听器的映射表
    this.listeners = new Map();
    
    // 调试模式标志
    this.debug = false;
  }

  /**
   * 注册事件监听器
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   * @param {Object} context - 执行上下文（可选）
   * @returns {Function} 取消监听的函数
   */
  on(eventName, callback, context = null) {
    if (typeof eventName !== 'string' || typeof callback !== 'function') {
      throw new Error('事件名称必须是字符串，回调必须是函数');
    }

    // 如果事件不存在，创建新的监听器数组
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, []);
    }

    // 创建监听器对象
    const listener = {
      callback,
      context,
      id: this.generateListenerId()
    };

    // 添加到监听器列表
    this.listeners.get(eventName).push(listener);

    if (this.debug) {
      console.log(`[EventManager] 注册事件监听器: ${eventName}`, listener);
    }

    // 返回取消监听的函数
    return () => this.off(eventName, listener.id);
  }

  /**
   * 移除事件监听器
   * @param {string} eventName - 事件名称
   * @param {string|Function} listenerIdOrCallback - 监听器ID或回调函数
   */
  off(eventName, listenerIdOrCallback) {
    if (!this.listeners.has(eventName)) {
      return;
    }

    const listeners = this.listeners.get(eventName);
    
    // 根据ID或回调函数移除监听器
    const index = listeners.findIndex(listener => {
      if (typeof listenerIdOrCallback === 'string') {
        return listener.id === listenerIdOrCallback;
      } else if (typeof listenerIdOrCallback === 'function') {
        return listener.callback === listenerIdOrCallback;
      }
      return false;
    });

    if (index !== -1) {
      listeners.splice(index, 1);
      
      if (this.debug) {
        console.log(`[EventManager] 移除事件监听器: ${eventName}`);
      }

      // 如果没有监听器了，删除事件
      if (listeners.length === 0) {
        this.listeners.delete(eventName);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} eventName - 事件名称
   * @param {*} data - 事件数据
   * @returns {boolean} 是否有监听器处理了事件
   */
  emit(eventName, data = null) {
    if (!this.listeners.has(eventName)) {
      if (this.debug) {
        console.log(`[EventManager] 没有找到事件监听器: ${eventName}`);
      }
      return false;
    }

    const listeners = this.listeners.get(eventName);
    let handled = false;

    if (this.debug) {
      console.log(`[EventManager] 触发事件: ${eventName}`, data);
    }

    // 复制监听器数组，避免在执行过程中被修改
    const listenersToCall = [...listeners];

    // 依次调用所有监听器
    listenersToCall.forEach(listener => {
      try {
        if (listener.context) {
          listener.callback.call(listener.context, data);
        } else {
          listener.callback(data);
        }
        handled = true;
      } catch (error) {
        console.error(`[EventManager] 事件处理器执行错误 (${eventName}):`, error);
      }
    });

    return handled;
  }

  /**
   * 一次性事件监听器
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   * @param {Object} context - 执行上下文（可选）
   */
  once(eventName, callback, context = null) {
    const onceWrapper = (data) => {
      callback.call(context, data);
      this.off(eventName, onceWrapper);
    };

    return this.on(eventName, onceWrapper, context);
  }

  /**
   * 移除指定事件的所有监听器
   * @param {string} eventName - 事件名称
   */
  removeAllListeners(eventName) {
    if (eventName) {
      this.listeners.delete(eventName);
      if (this.debug) {
        console.log(`[EventManager] 移除所有监听器: ${eventName}`);
      }
    } else {
      // 如果没有指定事件名，清除所有监听器
      this.listeners.clear();
      if (this.debug) {
        console.log('[EventManager] 清除所有事件监听器');
      }
    }
  }

  /**
   * 获取事件的监听器数量
   * @param {string} eventName - 事件名称
   * @returns {number} 监听器数量
   */
  getListenerCount(eventName) {
    return this.listeners.has(eventName) ? this.listeners.get(eventName).length : 0;
  }

  /**
   * 获取所有注册的事件名称
   * @returns {Array<string>} 事件名称数组
   */
  getEventNames() {
    return Array.from(this.listeners.keys());
  }

  /**
   * 启用或禁用调试模式
   * @param {boolean} enabled - 是否启用调试
   */
  setDebug(enabled) {
    this.debug = enabled;
    console.log(`[EventManager] 调试模式: ${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 生成唯一的监听器ID
   * @returns {string} 监听器ID
   */
  generateListenerId() {
    return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁事件管理器
   */
  destroy() {
    this.removeAllListeners();
    if (this.debug) {
      console.log('[EventManager] 事件管理器已销毁');
    }
  }
}

// 导出事件管理器类
window.EventManager = EventManager;
