/**
 * DOM操作工具类
 * 提供常用的DOM操作方法，简化DOM操作代码
 */
window.DOMUtils = {

  /**
   * 获取单个DOM元素
   * @param {string} selector - CSS选择器
   * @param {Element} parent - 父元素（可选）
   * @returns {Element|null} DOM元素
   */
  $(selector, parent = document) {
    return parent.querySelector(selector);
  },

  /**
   * 获取多个DOM元素
   * @param {string} selector - CSS选择器
   * @param {Element} parent - 父元素（可选）
   * @returns {NodeList} DOM元素列表
   */
  $$(selector, parent = document) {
    return parent.querySelectorAll(selector);
  },

  /**
   * 根据ID获取元素
   * @param {string} id - 元素ID
   * @returns {Element|null} DOM元素
   */
  getElementById(id) {
    return document.getElementById(id);
  },

  /**
   * 创建DOM元素
   * @param {string} tagName - 标签名
   * @param {Object} attributes - 属性对象（可选）
   * @param {string} textContent - 文本内容（可选）
   * @returns {Element} 创建的DOM元素
   */
  createElement(tagName, attributes = {}, textContent = '') {
    const element = document.createElement(tagName);
    
    // 设置属性
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className' || key === 'class') {
        element.className = value;
      } else if (key === 'dataset') {
        Object.entries(value).forEach(([dataKey, dataValue]) => {
          element.dataset[dataKey] = dataValue;
        });
      } else {
        element.setAttribute(key, value);
      }
    });

    // 设置文本内容
    if (textContent) {
      element.textContent = textContent;
    }

    return element;
  },

  /**
   * 添加CSS类
   * @param {Element|string} element - DOM元素或选择器
   * @param {string|Array} className - 类名或类名数组
   */
  addClass(element, className) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    if (Array.isArray(className)) {
      el.classList.add(...className);
    } else {
      el.classList.add(className);
    }
  },

  /**
   * 移除CSS类
   * @param {Element|string} element - DOM元素或选择器
   * @param {string|Array} className - 类名或类名数组
   */
  removeClass(element, className) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    if (Array.isArray(className)) {
      el.classList.remove(...className);
    } else {
      el.classList.remove(className);
    }
  },

  /**
   * 切换CSS类
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} className - 类名
   * @returns {boolean} 切换后是否包含该类
   */
  toggleClass(element, className) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return false;

    return el.classList.toggle(className);
  },

  /**
   * 检查是否包含CSS类
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} className - 类名
   * @returns {boolean} 是否包含该类
   */
  hasClass(element, className) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return false;

    return el.classList.contains(className);
  },

  /**
   * 设置元素属性
   * @param {Element|string} element - DOM元素或选择器
   * @param {string|Object} attr - 属性名或属性对象
   * @param {string} value - 属性值（当attr为字符串时）
   */
  setAttr(element, attr, value) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    if (typeof attr === 'object') {
      Object.entries(attr).forEach(([key, val]) => {
        el.setAttribute(key, val);
      });
    } else {
      el.setAttribute(attr, value);
    }
  },

  /**
   * 获取元素属性
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} attr - 属性名
   * @returns {string|null} 属性值
   */
  getAttr(element, attr) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return null;

    return el.getAttribute(attr);
  },

  /**
   * 设置元素样式
   * @param {Element|string} element - DOM元素或选择器
   * @param {string|Object} style - 样式属性名或样式对象
   * @param {string} value - 样式值（当style为字符串时）
   */
  setStyle(element, style, value) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    if (typeof style === 'object') {
      Object.entries(style).forEach(([key, val]) => {
        el.style[key] = val;
      });
    } else {
      el.style[style] = value;
    }
  },

  /**
   * 设置元素内容
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} content - 内容
   * @param {boolean} isHTML - 是否为HTML内容
   */
  setContent(element, content, isHTML = false) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    if (isHTML) {
      el.innerHTML = content;
    } else {
      el.textContent = content;
    }
  },

  /**
   * 获取元素内容
   * @param {Element|string} element - DOM元素或选择器
   * @param {boolean} isHTML - 是否获取HTML内容
   * @returns {string} 元素内容
   */
  getContent(element, isHTML = false) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return '';

    return isHTML ? el.innerHTML : el.textContent;
  },

  /**
   * 显示元素
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} display - 显示方式（可选）
   */
  show(element, display = 'block') {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    el.style.display = display;
  },

  /**
   * 隐藏元素
   * @param {Element|string} element - DOM元素或选择器
   */
  hide(element) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    el.style.display = 'none';
  },

  /**
   * 切换元素显示/隐藏
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} display - 显示方式（可选）
   */
  toggle(element, display = 'block') {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    if (el.style.display === 'none') {
      this.show(el, display);
    } else {
      this.hide(el);
    }
  },

  /**
   * 添加事件监听器
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} event - 事件类型
   * @param {Function} handler - 事件处理函数
   * @param {boolean|Object} options - 事件选项
   */
  on(element, event, handler, options = false) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    el.addEventListener(event, handler, options);
  },

  /**
   * 移除事件监听器
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} event - 事件类型
   * @param {Function} handler - 事件处理函数
   * @param {boolean|Object} options - 事件选项
   */
  off(element, event, handler, options = false) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    el.removeEventListener(event, handler, options);
  },

  /**
   * 触发自定义事件
   * @param {Element|string} element - DOM元素或选择器
   * @param {string} eventType - 事件类型
   * @param {*} detail - 事件详情数据
   */
  trigger(element, eventType, detail = null) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return;

    const event = new CustomEvent(eventType, {
      detail,
      bubbles: true,
      cancelable: true
    });

    el.dispatchEvent(event);
  },

  /**
   * 检查元素是否可见
   * @param {Element|string} element - DOM元素或选择器
   * @returns {boolean} 是否可见
   */
  isVisible(element) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return false;

    return el.offsetWidth > 0 && el.offsetHeight > 0;
  },

  /**
   * 获取元素位置信息
   * @param {Element|string} element - DOM元素或选择器
   * @returns {Object} 位置信息对象
   */
  getPosition(element) {
    const el = typeof element === 'string' ? this.$(element) : element;
    if (!el) return null;

    const rect = el.getBoundingClientRect();
    return {
      top: rect.top,
      left: rect.left,
      right: rect.right,
      bottom: rect.bottom,
      width: rect.width,
      height: rect.height
    };
  },

  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Function} 防抖后的函数
   */
  debounce(func, delay) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  },

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Function} 节流后的函数
   */
  throttle(func, delay) {
    let lastCall = 0;
    return function (...args) {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func.apply(this, args);
      }
    };
  }
};
