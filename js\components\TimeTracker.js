/**
 * 时间追踪组件
 * 负责学习时间追踪、计时器管理
 */
class TimeTracker {
  constructor(eventManager) {
    this.eventManager = eventManager;
    
    // 计时器状态
    this.currentTimer = TimeUtils.createTimer();
    this.currentStudyItem = null;
    this.timerInterval = null;
    this.isVisible = false;

    // DOM元素引用
    this.elements = {
      timeTracker: DOMUtils.$(AppConstants.SELECTORS.TIME_TRACKER),
      closeTracker: DOMUtils.$(AppConstants.SELECTORS.CLOSE_TRACKER),
      currentItem: DOMUtils.$(AppConstants.SELECTORS.CURRENT_ITEM),
      timerDisplay: DOMUtils.$(AppConstants.SELECTORS.TIMER_DISPLAY),
      startTimer: DOMUtils.$(AppConstants.SELECTORS.START_TIMER),
      pauseTimer: DOMUtils.$(AppConstants.SELECTORS.PAUSE_TIMER),
      stopTimer: DOMUtils.$(AppConstants.SELECTORS.STOP_TIMER),
      sessionNotes: DOMUtils.$(AppConstants.SELECTORS.SESSION_NOTES),
      saveNotes: DOMUtils.$(AppConstants.SELECTORS.SAVE_NOTES),
      modalOverlay: DOMUtils.$(AppConstants.SELECTORS.MODAL_OVERLAY)
    };

    this.init();
  }

  /**
   * 初始化时间追踪组件
   */
  init() {
    this.bindEvents();
    this.updateTimerDisplay();
    this.updateButtonStates();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听学习会话开始事件
    this.eventManager.on(AppConstants.EVENTS.STUDY_SESSION_STARTED, (data) => {
      this.startStudySession(data.item);
    });

    // 绑定控制按钮事件
    if (this.elements.closeTracker) {
      DOMUtils.on(this.elements.closeTracker, 'click', () => this.closeTimeTracker());
    }

    if (this.elements.startTimer) {
      DOMUtils.on(this.elements.startTimer, 'click', () => this.startTimer());
    }

    if (this.elements.pauseTimer) {
      DOMUtils.on(this.elements.pauseTimer, 'click', () => this.pauseTimer());
    }

    if (this.elements.stopTimer) {
      DOMUtils.on(this.elements.stopTimer, 'click', () => this.stopTimer());
    }

    if (this.elements.saveNotes) {
      DOMUtils.on(this.elements.saveNotes, 'click', () => this.saveSessionNotes());
    }

    // 模态框关闭事件
    if (this.elements.modalOverlay) {
      DOMUtils.on(this.elements.modalOverlay, 'click', () => this.closeTimeTracker());
    }

    // 键盘快捷键
    DOMUtils.on(document, 'keydown', (e) => this.handleKeyboardShortcuts(e));
  }

  /**
   * 开始学习会话
   * @param {Object} item - 学习项目对象
   */
  startStudySession(item) {
    if (!item) {
      console.error('未找到指定的学习项目');
      return;
    }

    console.log('开始学习会话:', item.title);

    this.currentStudyItem = item;
    
    // 更新当前项目显示
    if (this.elements.currentItem) {
      DOMUtils.setContent(this.elements.currentItem, item.title);
    }

    // 重置计时器
    this.currentTimer.reset();
    this.updateTimerDisplay();
    this.updateButtonStates();

    // 显示时间追踪面板
    this.showTimeTracker();

    // 触发会话开始事件
    this.eventManager.emit(AppConstants.EVENTS.STUDY_SESSION_STARTED, {
      item: item,
      startTime: Date.now()
    });

    console.log('时间追踪面板已打开');
  }

  /**
   * 显示时间追踪面板
   */
  showTimeTracker() {
    if (this.elements.timeTracker) {
      DOMUtils.addClass(this.elements.timeTracker, AppConstants.CSS_CLASSES.ACTIVE);
    }
    
    if (this.elements.modalOverlay) {
      DOMUtils.addClass(this.elements.modalOverlay, AppConstants.CSS_CLASSES.ACTIVE);
    }

    this.isVisible = true;

    // 触发模态框打开事件
    this.eventManager.emit(AppConstants.EVENTS.MODAL_OPENED, {
      type: 'timeTracker'
    });
  }

  /**
   * 关闭时间追踪面板
   */
  closeTimeTracker() {
    if (this.elements.timeTracker) {
      DOMUtils.removeClass(this.elements.timeTracker, AppConstants.CSS_CLASSES.ACTIVE);
    }
    
    if (this.elements.modalOverlay) {
      DOMUtils.removeClass(this.elements.modalOverlay, AppConstants.CSS_CLASSES.ACTIVE);
    }

    this.isVisible = false;

    // 如果计时器正在运行，停止它
    if (this.currentTimer.isRunning) {
      this.stopTimer();
    }

    // 触发模态框关闭事件
    this.eventManager.emit(AppConstants.EVENTS.MODAL_CLOSED, {
      type: 'timeTracker'
    });
  }

  /**
   * 开始计时
   */
  startTimer() {
    if (!this.currentStudyItem) {
      alert('请先选择学习项目');
      return;
    }

    this.currentTimer.start();
    this.startTimerInterval();
    this.updateButtonStates();

    // 触发计时器开始事件
    this.eventManager.emit(AppConstants.EVENTS.TIMER_STARTED, {
      item: this.currentStudyItem,
      startTime: Date.now()
    });

    console.log('计时器已开始');
  }

  /**
   * 暂停计时
   */
  pauseTimer() {
    this.currentTimer.pause();
    this.stopTimerInterval();
    this.updateButtonStates();

    // 触发计时器暂停事件
    this.eventManager.emit(AppConstants.EVENTS.TIMER_PAUSED, {
      item: this.currentStudyItem,
      elapsedTime: this.currentTimer.getElapsed()
    });

    console.log('计时器已暂停');
  }

  /**
   * 恢复计时
   */
  resumeTimer() {
    this.currentTimer.resume();
    this.startTimerInterval();
    this.updateButtonStates();

    console.log('计时器已恢复');
  }

  /**
   * 停止计时
   */
  stopTimer() {
    const elapsedTime = this.currentTimer.stop();
    this.stopTimerInterval();
    this.updateButtonStates();

    if (this.currentStudyItem && elapsedTime > 0) {
      // 触发学习会话结束事件
      this.eventManager.emit(AppConstants.EVENTS.STUDY_SESSION_ENDED, {
        itemId: this.currentStudyItem.id,
        timeSpent: elapsedTime,
        endTime: Date.now()
      });

      console.log(`学习会话结束，用时: ${TimeUtils.formatTime(elapsedTime)}`);
    }

    // 重置状态
    this.currentStudyItem = null;
    this.updateTimerDisplay();

    // 触发计时器停止事件
    this.eventManager.emit(AppConstants.EVENTS.TIMER_STOPPED, {
      totalTime: elapsedTime
    });

    console.log('计时器已停止');
  }

  /**
   * 开始计时器间隔更新
   */
  startTimerInterval() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    this.timerInterval = setInterval(() => {
      this.updateTimerDisplay();
      
      // 触发计时器更新事件
      this.eventManager.emit(AppConstants.EVENTS.TIMER_UPDATED, {
        elapsedTime: this.currentTimer.getElapsed(),
        formattedTime: this.currentTimer.getFormattedElapsed()
      });
    }, AppConstants.CONFIG.TIMER_INTERVAL);
  }

  /**
   * 停止计时器间隔更新
   */
  stopTimerInterval() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * 更新计时器显示
   */
  updateTimerDisplay() {
    if (this.elements.timerDisplay) {
      const formattedTime = this.currentTimer.getFormattedElapsed();
      DOMUtils.setContent(this.elements.timerDisplay, formattedTime);
    }
  }

  /**
   * 更新按钮状态
   */
  updateButtonStates() {
    const isRunning = this.currentTimer.isRunning;
    const isPaused = this.currentTimer.isPaused;
    const hasItem = !!this.currentStudyItem;

    // 开始按钮
    if (this.elements.startTimer) {
      this.elements.startTimer.disabled = isRunning && !isPaused;
      this.elements.startTimer.textContent = (isRunning && isPaused) ? '恢复' : '开始';
    }

    // 暂停按钮
    if (this.elements.pauseTimer) {
      this.elements.pauseTimer.disabled = !isRunning || isPaused;
    }

    // 停止按钮
    if (this.elements.stopTimer) {
      this.elements.stopTimer.disabled = !hasItem;
    }
  }

  /**
   * 保存会话笔记
   */
  saveSessionNotes() {
    if (!this.currentStudyItem) {
      alert('请先选择学习项目');
      return;
    }

    if (!this.elements.sessionNotes) {
      return;
    }

    const notes = this.elements.sessionNotes.value.trim();
    if (notes) {
      // 触发笔记保存事件
      this.eventManager.emit('notes:save', {
        itemId: this.currentStudyItem.id,
        notes: notes
      });

      // 清空笔记输入框
      this.elements.sessionNotes.value = '';
      
      // 显示成功消息
      alert(AppConstants.SUCCESS_MESSAGES.NOTES_SAVED);
    } else {
      alert('请输入笔记内容');
    }
  }

  /**
   * 处理键盘快捷键
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleKeyboardShortcuts(event) {
    // 只在时间追踪面板可见时处理快捷键
    if (!this.isVisible) return;

    // Ctrl/Cmd + Space: 开始/暂停计时
    if ((event.ctrlKey || event.metaKey) && event.code === 'Space') {
      event.preventDefault();
      
      if (this.currentTimer.isRunning && !this.currentTimer.isPaused) {
        this.pauseTimer();
      } else if (this.currentTimer.isPaused) {
        this.resumeTimer();
      } else {
        this.startTimer();
      }
    }

    // Escape: 关闭面板
    if (event.code === 'Escape') {
      event.preventDefault();
      this.closeTimeTracker();
    }

    // Ctrl/Cmd + Enter: 保存笔记
    if ((event.ctrlKey || event.metaKey) && event.code === 'Enter') {
      event.preventDefault();
      this.saveSessionNotes();
    }
  }

  /**
   * 获取当前计时状态
   * @returns {Object} 计时状态对象
   */
  getCurrentStatus() {
    return {
      isRunning: this.currentTimer.isRunning,
      isPaused: this.currentTimer.isPaused,
      elapsedTime: this.currentTimer.getElapsed(),
      formattedTime: this.currentTimer.getFormattedElapsed(),
      currentItem: this.currentStudyItem ? {
        id: this.currentStudyItem.id,
        title: this.currentStudyItem.title
      } : null,
      isVisible: this.isVisible
    };
  }

  /**
   * 强制停止所有计时器
   */
  forceStop() {
    this.stopTimer();
    this.closeTimeTracker();
  }

  /**
   * 检查是否有活动的学习会话
   * @returns {boolean} 是否有活动会话
   */
  hasActiveSession() {
    return this.currentTimer.isRunning || !!this.currentStudyItem;
  }

  /**
   * 销毁组件
   */
  destroy() {
    // 停止计时器
    this.forceStop();

    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.STUDY_SESSION_STARTED);
    
    // 清理间隔
    this.stopTimerInterval();

    // 清理数据
    this.currentTimer = null;
    this.currentStudyItem = null;
    this.elements = {};
  }
}

// 导出组件
window.TimeTracker = TimeTracker;
