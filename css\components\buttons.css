/* ===== 按钮组件样式 ===== */

/* 基础按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  transform: none !important;
  box-shadow: none !important;
}

/* 主要按钮 */
.btn-primary {
  /* {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-27 23:45:00 +08:00]
  // Reason: 合并.btn基础样式到.btn-primary，解决按钮样式丢失问题
  // Principle_Applied: DRY - 虽然复制了基础样式，但确保了组件的独立性和完整性
  // Optimization: 将基础样式与特定样式合并，提高CSS选择器的独立性
  // Architectural_Note (AR): 符合组件化设计原则，每个按钮类型都是完整的组件
  // Documentation_Note (DW): 相关文档在 /project_document/Learning_Router_Analysis.md 中已更新
  // }} */
  /* {{START MODIFICATIONS}} */
  /* + 基础按钮样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;

  /* + 主要按钮特定样式 */
  background: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
  /* {{END MODIFICATIONS}} */
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-primary:disabled {
  background: var(--text-muted);
  border-color: var(--text-muted);
}

/* 次要按钮 */
.btn-secondary {
  /* {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-27 23:46:00 +08:00]
  // Reason: 合并.btn基础样式到.btn-secondary，解决按钮样式丢失问题
  // Principle_Applied: DRY - 确保组件的独立性和完整性
  // Optimization: 将基础样式与特定样式合并
  // Architectural_Note (AR): 符合组件化设计原则
  // }} */
  /* {{START MODIFICATIONS}} */
  /* + 基础按钮样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;

  /* + 次要按钮特定样式 */
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  /* {{END MODIFICATIONS}} */
}

.btn-secondary:hover:not(:disabled) {
  background: var(--border-color);
  border-color: var(--border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:active:not(:disabled) {
  transform: translateY(0);
  background: var(--border-hover);
}

/* 图标按钮 */
.btn-icon {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  min-width: 40px;
  min-height: 40px;
}

.btn-icon:hover:not(:disabled) {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-icon:active:not(:disabled) {
  background: var(--border-color);
}

/* 复选框按钮 */
.btn-checkbox {
  background: none;
  border: 2px solid var(--border-color);
  padding: var(--spacing-xs);
  border-radius: 4px;
  color: transparent;
  font-size: var(--font-size-sm);
  min-width: 20px;
  min-height: 20px;
  position: relative;
}

.btn-checkbox:hover:not(:disabled) {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.btn-checkbox.checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-checkbox .checkmark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: var(--transition);
}

.btn-checkbox.checked .checkmark {
  opacity: 1;
}

/* 计时器按钮 */
.btn-timer {
  background: none;
  border: 1px solid var(--border-color);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  min-width: 32px;
  min-height: 32px;
}

.btn-timer:hover:not(:disabled) {
  background: var(--primary-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-timer:active:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

/* 折叠按钮 */
.collapse-btn {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  min-width: 24px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.collapse-btn:hover:not(:disabled) {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.collapse-btn .collapse-icon {
  transition: transform 0.2s ease;
}

.collapse-btn.collapsed .collapse-icon {
  transform: rotate(90deg);
}

/* 按钮尺寸变体 */
.btn-xs {
  padding: 2px var(--spacing-xs);
  font-size: var(--font-size-xs);
  min-height: 24px;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: 32px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
  min-height: 48px;
}

.btn-xl {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 56px;
}

/* 按钮宽度变体 */
.btn-full {
  width: 100%;
}

.btn-auto {
  width: auto;
}

/* 按钮组 */
.btn-group {
  display: inline-flex;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
  position: relative;
  z-index: 1;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  border-right-width: 1px;
}

.btn-group .btn:hover,
.btn-group .btn:focus {
  z-index: 2;
}

/* 按钮状态变体 */
.btn-success {
  /* {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-27 23:47:00 +08:00]
  // Reason: 合并.btn基础样式到.btn-success，解决按钮样式丢失问题
  // Principle_Applied: DRY - 确保组件的独立性和完整性
  // }} */
  /* {{START MODIFICATIONS}} */
  /* + 基础按钮样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;

  /* + 成功按钮特定样式 */
  background: var(--success-color);
  color: white;
  border: 1px solid var(--success-color);
  /* {{END MODIFICATIONS}} */
}

.btn-success:hover:not(:disabled) {
  background: #059669;
  border-color: #059669;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  /* {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-27 23:48:00 +08:00]
  // Reason: 合并.btn基础样式到.btn-warning，解决按钮样式丢失问题
  // Principle_Applied: DRY - 确保组件的独立性和完整性
  // }} */
  /* {{START MODIFICATIONS}} */
  /* + 基础按钮样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;

  /* + 警告按钮特定样式 */
  background: var(--warning-color);
  color: white;
  border: 1px solid var(--warning-color);
  /* {{END MODIFICATIONS}} */
}

.btn-warning:hover:not(:disabled) {
  background: #D97706;
  border-color: #D97706;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-error {
  /* {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-27 23:49:00 +08:00]
  // Reason: 合并.btn基础样式到.btn-error，解决按钮样式丢失问题
  // Principle_Applied: DRY - 确保组件的独立性和完整性
  // }} */
  /* {{START MODIFICATIONS}} */
  /* + 基础按钮样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;

  /* + 错误按钮特定样式 */
  background: var(--error-color);
  color: white;
  border: 1px solid var(--error-color);
  /* {{END MODIFICATIONS}} */
}

.btn-error:hover:not(:disabled) {
  background: #DC2626;
  border-color: #DC2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 幽灵按钮（透明背景） */
.btn-ghost {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

/* 链接样式按钮 */
.btn-link {
  background: none;
  border: none;
  color: var(--primary-color);
  text-decoration: underline;
  padding: 0;
  min-height: auto;
}

.btn-link:hover:not(:disabled) {
  color: var(--primary-hover);
  background: none;
  transform: none;
  box-shadow: none;
}

/* 加载状态 */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 主题切换按钮特殊样式 */
.theme-switching {
  animation: theme-switch 0.3s ease;
}

@keyframes theme-switch {
  0% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
  100% { transform: scale(1) rotate(360deg); }
}

/* 响应式按钮 */
@media (max-width: 768px) {
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  .btn-lg {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    min-height: 44px;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0;
    border-bottom-width: 0;
    border-right-width: 1px;
  }

  .btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    border-bottom-left-radius: 0;
  }

  .btn-group .btn:last-child {
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    border-top-right-radius: 0;
    border-bottom-width: 1px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .btn-icon {
    min-width: 44px;
    min-height: 44px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }

  .btn-primary {
    background: #000000;
    color: #FFFFFF;
    border-color: #000000;
  }

  .btn-secondary {
    background: #FFFFFF;
    color: #000000;
    border-color: #000000;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }

  .btn:hover {
    transform: none;
  }

  .collapse-btn .collapse-icon {
    transition: none;
  }

  .btn-loading::after {
    animation: none;
  }
}
