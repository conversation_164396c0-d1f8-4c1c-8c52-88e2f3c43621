/* ===== 模态框组件样式 ===== */

/* 模态框遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  z-index: var(--z-index-modal-backdrop);
  backdrop-filter: blur(2px);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* 基础模态框 */
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  z-index: var(--z-index-modal);
  overflow: hidden;
}

.modal.active {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

/* 模态框头部 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  transition: var(--transition);
  line-height: 1;
}

.modal-close:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* 模态框内容 */
.modal-content {
  padding: var(--spacing-lg);
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.modal-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

/* 时间追踪面板（特殊模态框） */
.time-tracker {
  position: fixed;
  right: -400px;
  top: 50%;
  transform: translateY(-50%);
  width: 350px;
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  transition: right 0.3s ease;
  z-index: var(--z-index-modal);
  overflow: hidden;
}

.time-tracker.active {
  right: var(--spacing-md);
}

.tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.tracker-header h3 {
  font-size: var(--font-size-lg);
  margin: 0;
  color: var(--text-primary);
}

.tracker-content {
  padding: var(--spacing-md);
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 当前会话信息 */
.current-session {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.current-session h4 {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.current-session p {
  font-weight: var(--font-weight-medium);
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

/* 计时器显示 */
.timer-display {
  text-align: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.timer {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: var(--font-family-mono);
  margin-bottom: var(--spacing-sm);
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 计时器控制按钮 */
.timer-controls {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.timer-controls button {
  flex: 1;
  max-width: 80px;
  min-height: 36px;
  font-size: var(--font-size-sm);
}

/* 模态框尺寸变体 */
.modal-sm {
  max-width: 300px;
}

.modal-md {
  max-width: 500px;
}

.modal-lg {
  max-width: 800px;
}

.modal-xl {
  max-width: 1200px;
}

.modal-full {
  width: 95%;
  height: 95%;
  max-width: none;
  max-height: none;
  top: 2.5%;
  left: 2.5%;
  transform: none;
}

.modal-full.active {
  transform: none;
}

/* 模态框动画变体 */
.modal-fade-in {
  animation: modal-fade-in 0.3s ease;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.modal-slide-up {
  animation: modal-slide-up 0.3s ease;
}

@keyframes modal-slide-up {
  from {
    opacity: 0;
    transform: translate(-50%, -30%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 成功通知 */
.success-notification {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--success-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-index-tooltip);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.success-notification.show {
  transform: translateX(0);
}

.success-notification::before {
  content: '✓';
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

/* 确认对话框 */
.confirm-dialog {
  text-align: center;
}

.confirm-dialog .modal-content {
  padding: var(--spacing-xl);
}

.confirm-dialog h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.confirm-dialog p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-relaxed);
}

.confirm-dialog .modal-footer {
  justify-content: center;
  gap: var(--spacing-md);
}

/* 响应式模态框 */
@media (max-width: 768px) {
  .modal {
    width: 95%;
    max-width: none;
    margin: var(--spacing-sm);
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding: var(--spacing-md);
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .time-tracker {
    width: calc(100vw - 2 * var(--spacing-md));
    right: calc(-100vw + var(--spacing-md));
    top: var(--spacing-md);
    bottom: var(--spacing-md);
    transform: none;
    height: auto;
  }
  
  .time-tracker.active {
    right: var(--spacing-md);
  }
  
  .timer {
    font-size: 2rem;
  }
  
  .timer-controls {
    flex-wrap: wrap;
  }
  
  .timer-controls button {
    flex: 1 1 calc(50% - var(--spacing-xs));
    max-width: none;
  }
  
  .success-notification {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    transform: translateY(-100%);
  }
  
  .success-notification.show {
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .modal {
    width: 100%;
    height: 100%;
    max-height: none;
    border-radius: 0;
    top: 0;
    left: 0;
    transform: none;
  }
  
  .modal.active {
    transform: none;
  }
  
  .modal-content {
    max-height: calc(100vh - 120px);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .modal-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .modal {
    border-width: 2px;
    border-color: #000000;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .modal,
  .modal-overlay,
  .time-tracker,
  .success-notification {
    transition: none;
  }
  
  .modal-fade-in,
  .modal-slide-up {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .modal-overlay,
  .modal,
  .time-tracker,
  .success-notification {
    display: none !important;
  }
}
