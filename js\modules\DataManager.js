/**
 * 数据管理模块
 * 负责数据持久化、状态管理和CRUD操作
 */
class DataManager {
  constructor(eventManager) {
    this.eventManager = eventManager;
    this.roadmapData = [];
    this.learningProgress = {};
    this.studyTime = {};

    this.init();
  }

  /**
   * 初始化数据管理模块
   */
  init() {
    this.bindEvents();
    this.loadStoredData();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听路线图更新事件
    this.eventManager.on(AppConstants.EVENTS.ROADMAP_UPDATED, (data) => {
      this.setRoadmapData(data.roadmapData);
    });

    // 监听项目完成状态变化
    this.eventManager.on(AppConstants.EVENTS.ITEM_COMPLETED, (data) => {
      this.updateItemCompletion(data.id, data.completed);
    });

    // 监听项目折叠状态变化
    this.eventManager.on(AppConstants.EVENTS.ITEM_COLLAPSED, (data) => {
      this.updateItemCollapse(data.id, data.collapsed);
    });

    // 监听数据清空事件
    this.eventManager.on(AppConstants.EVENTS.DATA_CLEARED, () => {
      this.clearAllData();
    });

    // 监听学习时间更新
    this.eventManager.on(AppConstants.EVENTS.STUDY_SESSION_ENDED, (data) => {
      this.updateStudyTime(data.itemId, data.timeSpent);
    });
  }

  /**
   * 设置路线图数据
   * @param {Array} data - 路线图数据
   */
  setRoadmapData(data) {
    if (Array.isArray(data)) {
      this.roadmapData = data;
      this.saveData();

      // 触发统计更新
      this.eventManager.emit(AppConstants.EVENTS.STATS_UPDATED, {
        stats: this.getStats()
      });
    }
  }

  /**
   * 获取路线图数据
   * @returns {Array} 路线图数据
   */
  getRoadmapData() {
    return this.roadmapData;
  }

  /**
   * 根据ID查找项目
   * @param {number} id - 项目ID
   * @param {Array} items - 搜索范围
   * @returns {Object|null} 找到的项目或null
   */
  findItemById(id, items = this.roadmapData) {
    for (let item of items) {
      if (item.id === id) {
        return item;
      }
      if (item.children && item.children.length > 0) {
        const found = this.findItemById(id, item.children);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  /**
   * 更新项目完成状态
   * @param {number} id - 项目ID
   * @param {boolean} completed - 完成状态
   */
  updateItemCompletion(id, completed) {
    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-01-28 00:56:00 +08:00]
    // Reason: 添加事件触发时机的调试日志 - 任务4.3
    // Principle_Applied: 可维护性 - 验证事件触发的时序和数据正确性
    // Optimization: 确保状态同步问题修复后事件流程正常工作
    // }}
    const item = this.findItemById(id);
    if (item) {
      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-01-28 04:20:00 +08:00]
      // Reason: 添加级联完成功能 - 标记项目时同时标记所有子项目
      // Principle_Applied: 用户体验 - 符合用户期望的逻辑，标记课程模块时应该标记所有子内容
      // Optimization: 解决用户反馈的复选框逻辑问题
      // }}
      // 级联设置当前项目及其所有子项目的完成状态
      this.setCascadeCompletion(item, completed);
      this.saveData();

      // 项目状态更新日志已移除

      // 触发统计更新
      const stats = this.getStats();
      this.eventManager.emit(AppConstants.EVENTS.STATS_UPDATED, {
        stats: stats
      });

      // 触发进度更新
      const progress = this.getProgressPercentage();
      this.eventManager.emit(AppConstants.EVENTS.PROGRESS_UPDATED, {
        progress: progress
      });

      // 触发路线图更新，确保UI立即反映状态变化
      this.eventManager.emit(AppConstants.EVENTS.ROADMAP_UPDATED, {
        roadmapData: this.roadmapData
      });
    } else {
      console.warn('[DataManager] 未找到指定ID的项目:', id);
    }
  }

  /**
   * 切换项目完成状态
   * @param {number} id - 项目ID
   * @returns {boolean} 新的完成状态
   */
  toggleItemCompletion(id) {
    const item = this.findItemById(id);
    if (item) {
      const newStatus = !item.completed;
      this.updateItemCompletion(id, newStatus);
      return newStatus;
    }
    return false;
  }

  /**
   * 更新项目折叠状态
   * @param {number} id - 项目ID
   * @param {boolean} collapsed - 折叠状态
   */
  updateItemCollapse(id, collapsed) {
    const item = this.findItemById(id);
    if (item) {
      item.collapsed = collapsed;
      this.saveData();

      // {{CHENGQI:
      // Action: Added
      // Timestamp: [2025-01-28 03:50:00 +08:00]
      // Reason: 修复折叠功能 - 折叠状态更新后需要触发UI更新事件
      // Principle_Applied: 事件驱动架构 - 数据变化后通知UI更新
      // Optimization: 解决用户反馈的折叠按钮无法工作的问题
      // Architectural_Note (AR): 符合事件驱动架构，确保数据和UI同步
      // Documentation_Note (DW): 相关文档在 /project_document/Learning_Router_Analysis.md 中已更新
      // }}
      // 触发路线图更新事件，确保UI同步
      this.eventManager.emit(AppConstants.EVENTS.ROADMAP_UPDATED, {
        roadmapData: this.roadmapData
      });
    }
  }

  /**
   * 切换项目折叠状态
   * @param {number} id - 项目ID
   * @returns {boolean} 新的折叠状态
   */
  toggleItemCollapse(id) {
    const item = this.findItemById(id);
    if (item) {
      const newStatus = !item.collapsed;
      this.updateItemCollapse(id, newStatus);
      return newStatus;
    }
    return false;
  }

  /**
   * 更新学习时间
   * @param {number} id - 项目ID
   * @param {number} timeSpent - 学习时间（秒）
   */
  updateStudyTime(id, timeSpent) {
    const item = this.findItemById(id);
    if (item) {
      item.timeSpent = (item.timeSpent || 0) + timeSpent;
      this.saveData();

      // 触发统计更新
      this.eventManager.emit(AppConstants.EVENTS.STATS_UPDATED, {
        stats: this.getStats()
      });
    }
  }

  /**
   * 更新项目笔记
   * @param {number} id - 项目ID
   * @param {string} notes - 笔记内容
   */
  updateItemNotes(id, notes) {
    const item = this.findItemById(id);
    if (item) {
      item.notes = notes;
      this.saveData();
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息对象
   */
  getStats() {
    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-01-28 00:55:00 +08:00]
    // Reason: 添加统计数据计算过程的调试日志 - 任务4.3
    // Principle_Applied: 可维护性 - 详细日志便于验证统计计算逻辑正确性
    // Optimization: 帮助诊断状态同步问题，确保数据计算准确
    // }}
    const total = this.countAllItems();
    const completed = this.countCompletedItems();
    const totalTime = this.calculateTotalTime();
    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-01-28 01:50:00 +08:00]
    // Reason: 修复进度百分比计算，确保有完成项目时至少显示1% - 任务4.4
    // Principle_Applied: 用户体验 - 用户完成任务后应该看到进度变化
    // }}
    let progress = 0;
    if (total > 0) {
      progress = Math.round((completed / total) * 100);
      // 如果有已完成项目但计算结果为0%，至少显示1%
      if (completed > 0 && progress === 0) {
        progress = 1;
      }
    }

    const stats = {
      totalItems: total,
      completedItems: completed,
      totalTime: totalTime,
      progressPercent: progress,
      formattedTime: TimeUtils.formatTime(totalTime, false)
    };

    // 统计日志已移除

    return stats;
  }

  /**
   * 统计所有项目数量
   * @param {Array} items - 项目数组
   * @returns {number} 项目总数
   */
  countAllItems(items = this.roadmapData) {
    let count = 0;

    items.forEach(item => {
      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-01-28 01:45:00 +08:00]
      // Reason: 修改统计逻辑，将所有类型的项目都纳入统计 - 任务4.4
      // Principle_Applied: 用户体验 - 用户期望能够标记任何项目为完成并影响统计
      // Optimization: 解决统计数据不正确的根本问题
      // }}
      // 统计所有类型的项目，因为用户可以标记任何项目为完成
      count += 1;

      if (item.children && item.children.length > 0) {
        count += this.countAllItems(item.children);
      }
    });

    return count;
  }

  /**
   * 统计已完成项目数量
   * @param {Array} items - 项目数组
   * @returns {number} 已完成项目数
   */
  countCompletedItems(items = this.roadmapData) {
    let count = 0;

    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-01-28 01:30:00 +08:00]
    // Reason: 添加详细的已完成项目统计调试日志 - 任务4.4
    // Principle_Applied: 可维护性 - 详细日志便于诊断统计计算问题
    // }}
    items.forEach((item, index) => {
      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-01-28 01:45:00 +08:00]
      // Reason: 修改已完成项目统计逻辑，统计所有类型的已完成项目 - 任务4.4
      // Principle_Applied: 用户体验 - 用户期望标记任何项目为完成都能影响统计
      // Optimization: 解决统计数据始终为0的问题
      // }}
      // 统计所有类型的已完成项目
      if (item.completed) {
        count += 1;
      }

      if (item.children && item.children.length > 0) {
        const childCount = this.countCompletedItems(item.children);
        count += childCount;
      }
    });
    return count;
  }

  /**
   * 计算总学习时间
   * @param {Array} items - 项目数组
   * @returns {number} 总时间（秒）
   */
  calculateTotalTime(items = this.roadmapData) {
    let total = 0;

    const addTime = (item) => {
      if (item.timeSpent) {
        total += item.timeSpent;
      }
      if (item.children && item.children.length > 0) {
        item.children.forEach(addTime);
      }
    };

    items.forEach(addTime);
    return total;
  }

  /**
   * 获取进度百分比
   * @returns {number} 进度百分比
   */
  getProgressPercentage() {
    const total = this.countAllItems();
    const completed = this.countCompletedItems();
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  }

  /**
   * 重置所有进度
   */
  resetAllProgress() {
    const resetProgress = (items) => {
      items.forEach(item => {
        item.completed = false;
        item.timeSpent = 0;
        item.notes = '';
        if (item.children && item.children.length > 0) {
          resetProgress(item.children);
        }
      });
    };

    resetProgress(this.roadmapData);
    this.saveData();

    // 触发统计更新
    this.eventManager.emit(AppConstants.EVENTS.STATS_UPDATED, {
      stats: this.getStats()
    });

    // 触发进度更新
    this.eventManager.emit(AppConstants.EVENTS.PROGRESS_UPDATED, {
      progress: 0
    });
  }

  /**
   * 设置所有项目的折叠状态
   * @param {boolean} collapsed - 折叠状态
   */
  setAllItemsCollapsed(collapsed) {
    const setCollapsed = (items) => {
      items.forEach(item => {
        if (item.type === AppConstants.CONFIG.ITEM_TYPES.SECTION ||
            item.type === AppConstants.CONFIG.ITEM_TYPES.SUBSECTION) {
          item.collapsed = collapsed;
        }
        if (item.children && item.children.length > 0) {
          setCollapsed(item.children);
        }
      });
    };

    setCollapsed(this.roadmapData);
    this.saveData();
  }

  /**
   * 保存数据到本地存储
   */
  saveData() {
    const data = {
      roadmapData: this.roadmapData,
      timestamp: Date.now()
    };

    const success = StorageUtils.setLocal(
      AppConstants.CONFIG.STORAGE_KEYS.ROADMAP_DATA,
      data
    );

    if (success) {
      this.eventManager.emit(AppConstants.EVENTS.DATA_SAVED, { timestamp: data.timestamp });
    } else {
      console.error('数据保存失败');
      this.eventManager.emit(AppConstants.EVENTS.FILE_UPLOAD_ERROR, {
        message: AppConstants.ERROR_MESSAGES.STORAGE_ERROR
      });
    }
  }

  /**
   * 从本地存储加载数据
   */
  loadStoredData() {
    const stored = StorageUtils.getLocal(AppConstants.CONFIG.STORAGE_KEYS.ROADMAP_DATA);

    if (stored && stored.roadmapData) {
      try {
        this.roadmapData = stored.roadmapData;

        if (this.roadmapData.length > 0) {
          // 触发数据加载事件
          this.eventManager.emit(AppConstants.EVENTS.DATA_LOADED, {
            roadmapData: this.roadmapData,
            timestamp: stored.timestamp
          });

          // 触发统计更新
          this.eventManager.emit(AppConstants.EVENTS.STATS_UPDATED, {
            stats: this.getStats()
          });
        }
      } catch (error) {
        console.error('加载存储数据失败:', error);
        this.eventManager.emit(AppConstants.EVENTS.FILE_UPLOAD_ERROR, {
          message: AppConstants.ERROR_MESSAGES.LOAD_ERROR
        });
      }
    }
  }

  /**
   * 清空所有数据
   */
  clearAllData() {
    this.roadmapData = [];
    this.learningProgress = {};
    this.studyTime = {};

    // 清除本地存储
    StorageUtils.removeLocal(AppConstants.CONFIG.STORAGE_KEYS.ROADMAP_DATA);

    // 触发统计更新
    this.eventManager.emit(AppConstants.EVENTS.STATS_UPDATED, {
      stats: this.getStats()
    });
  }

  /**
   * 导出数据
   * @returns {Object} 导出的数据对象
   */
  exportData() {
    return {
      roadmapData: this.roadmapData,
      exportTime: Date.now(),
      version: '1.0'
    };
  }

  /**
   * 导入数据
   * @param {Object} data - 要导入的数据
   * @returns {boolean} 是否导入成功
   */
  importData(data) {
    if (data && Array.isArray(data.roadmapData)) {
      this.roadmapData = data.roadmapData;
      this.saveData();

      // 触发数据更新事件
      this.eventManager.emit(AppConstants.EVENTS.ROADMAP_UPDATED, {
        roadmapData: this.roadmapData
      });

      return true;
    }
    return false;
  }

  /**
   * 销毁模块
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.ROADMAP_UPDATED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.ITEM_COMPLETED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.ITEM_COLLAPSED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_CLEARED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.STUDY_SESSION_ENDED);

    // 清理数据
    this.roadmapData = [];
    this.learningProgress = {};
    this.studyTime = {};
  }
}

// 导出模块
window.DataManager = DataManager;
