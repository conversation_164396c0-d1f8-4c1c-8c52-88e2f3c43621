/* ===== 卡片组件样式 ===== */

/* 基础卡片样式 */
.card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 卡片头部 */
.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.card-header h2,
.card-header h3,
.card-header h4 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
}

.card-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 卡片内容 */
.card-content {
  padding: var(--spacing-md);
}

.card-content:last-child {
  padding-bottom: var(--spacing-md);
}

/* 卡片底部 */
.card-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  align-items: center;
}

/* 上传卡片 */
.upload-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
}

.upload-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.upload-header h2 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.upload-header p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

.upload-area {
  padding: var(--spacing-xl);
  border: 2px dashed var(--border-color);
  margin: var(--spacing-md);
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.upload-area.dragover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: scale(1.02);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.6;
  transition: var(--transition);
}

.upload-area:hover .upload-icon {
  opacity: 0.8;
  transform: scale(1.1);
}

.upload-text {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  margin: 0;
}

.upload-actions {
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

/* 统计卡片 */
.stat-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-card.stat-updated {
  animation: stat-pulse 0.6s ease;
}

@keyframes stat-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
  flex-shrink: 0;
  transition: var(--transition);
}

.stat-card:hover .stat-icon {
  opacity: 1;
  transform: scale(1.1);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
}

.stat-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
  font-weight: var(--font-weight-medium);
}

/* 进度卡片 */
.progress-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.progress-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin: 0;
}

.progress-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.progress-bar {
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  width: 0%;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 进度条颜色变体 */
.progress-fill.progress-low {
  background: linear-gradient(90deg, var(--progress-low), #DC2626);
}

.progress-fill.progress-medium {
  background: linear-gradient(90deg, var(--progress-medium), #D97706);
}

.progress-fill.progress-high {
  background: linear-gradient(90deg, var(--progress-high), #059669);
}

.progress-fill.progress-complete {
  background: linear-gradient(90deg, var(--progress-complete), #047857);
}

/* 空状态卡片 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  min-height: 400px;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.empty-icon {
  font-size: 4rem;
  opacity: 0.4;
  margin-bottom: var(--spacing-md);
  transition: var(--transition);
}

.empty-state:hover .empty-icon {
  opacity: 0.6;
  transform: scale(1.1);
}

.empty-state h3 {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-lg);
}

.empty-state p {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin: 0;
  max-width: 300px;
}

/* 卡片网格 */
.card-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.card-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.card-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 卡片尺寸变体 */
.card-sm {
  padding: var(--spacing-sm);
}

.card-lg {
  padding: var(--spacing-lg);
}

/* 卡片状态变体 */
.card-success {
  border-color: var(--success-color);
  background: var(--success-light);
}

.card-warning {
  border-color: var(--warning-color);
  background: var(--warning-light);
}

.card-error {
  border-color: var(--error-color);
  background: var(--error-light);
}

.card-info {
  border-color: var(--info-color);
  background: var(--info-light);
}

/* 响应式卡片 */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }
  
  .stat-icon {
    font-size: 1.5rem;
  }
  
  .stat-number {
    font-size: var(--font-size-2xl);
  }
  
  .upload-actions {
    flex-direction: column;
  }
  
  .card-footer {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .card {
    margin: 0 calc(-1 * var(--spacing-sm));
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .upload-area {
    margin: var(--spacing-sm);
    padding: var(--spacing-lg);
  }
  
  .upload-icon {
    font-size: 2.5rem;
  }
}

/* 打印样式 */
@media print {
  .card {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }
  
  .card:hover {
    transform: none;
  }
  
  .upload-area {
    display: none;
  }
}
