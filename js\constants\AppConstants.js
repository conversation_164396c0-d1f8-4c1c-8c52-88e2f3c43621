/**
 * 应用常量配置
 * 集中管理所有常量、选择器、事件名称等
 */
window.AppConstants = {
  
  // ===== DOM选择器 =====
  SELECTORS: {
    // 上传相关
    FILE_INPUT: '#fileInput',
    UPLOAD_AREA: '#uploadArea',
    PARSE_BTN: '#parseBtn',
    CLEAR_BTN: '#clearBtn',

    // 统计显示
    TOTAL_ITEMS: '#totalItems',
    COMPLETED_ITEMS: '#completedItems',
    TOTAL_TIME: '#totalTime',
    PROGRESS_PERCENT: '#progressPercent',
    PROGRESS_FILL: '#progressFill',

    // 控制按钮
    DARK_MODE_TOGGLE: '#darkModeToggle',
    COLLAPSE_ALL_BTN: '#collapseAllBtn',
    EXPAND_ALL_BTN: '#expandAllBtn',
    RESET_PROGRESS_BTN: '#resetProgressBtn',

    // 内容展示
    ROADMAP_CONTENT: '#roadmapContent',

    // 搜索和导航
    SEARCH_INPUT: '#searchInput',
    SIDEBAR_TOGGLE: '#sidebarToggle',
    CONTENT_TITLE: '#contentTitle',
    CONTENT_BODY: '#contentBody',
    MARK_COMPLETE_BTN: '#markCompleteBtn',
    START_STUDY_BTN: '#startStudyBtn',

    // 时间追踪
    TIME_TRACKER: '#timeTracker',
    CLOSE_TRACKER: '#closeTracker',
    CURRENT_ITEM: '#currentItem',
    TIMER_DISPLAY: '#timerDisplay',
    START_TIMER: '#startTimer',
    PAUSE_TIMER: '#pauseTimer',
    STOP_TIMER: '#stopTimer',
    SESSION_NOTES: '#sessionNotes',
    SAVE_NOTES: '#saveNotes',

    // 模态框
    MODAL_OVERLAY: '#modalOverlay'
  },

  // ===== 事件名称 =====
  EVENTS: {
    // 应用生命周期
    APP_INIT: 'app:init',
    APP_READY: 'app:ready',
    APP_DESTROY: 'app:destroy',

    // 文件处理
    FILE_SELECTED: 'file:selected',
    FILE_PARSED: 'file:parsed',
    FILE_UPLOAD_ERROR: 'file:upload_error',

    // 数据变化
    DATA_LOADED: 'data:loaded',
    DATA_SAVED: 'data:saved',
    DATA_CLEARED: 'data:cleared',
    ROADMAP_UPDATED: 'roadmap:updated',

    // 项目操作
    ITEM_SELECTED: 'item:selected',
    ITEM_COMPLETED: 'item:completed',
    ITEM_COLLAPSED: 'item:collapsed',
    ITEM_EXPANDED: 'item:expanded',

    // 统计更新
    STATS_UPDATED: 'stats:updated',
    PROGRESS_UPDATED: 'progress:updated',

    // 搜索功能
    SEARCH_PERFORMED: 'search:performed',
    SEARCH_CLEARED: 'search:cleared',

    // 主题切换
    THEME_CHANGED: 'theme:changed',

    // 时间追踪
    TIMER_STARTED: 'timer:started',
    TIMER_PAUSED: 'timer:paused',
    TIMER_STOPPED: 'timer:stopped',
    TIMER_UPDATED: 'timer:updated',
    STUDY_SESSION_STARTED: 'study:session_started',
    STUDY_SESSION_ENDED: 'study:session_ended',

    // UI状态
    SIDEBAR_TOGGLED: 'ui:sidebar_toggled',
    MODAL_OPENED: 'ui:modal_opened',
    MODAL_CLOSED: 'ui:modal_closed'
  },

  // ===== 配置参数 =====
  CONFIG: {
    // 文件上传
    ACCEPTED_FILE_TYPES: ['.md', '.txt'],
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB

    // 本地存储键名
    STORAGE_KEYS: {
      ROADMAP_DATA: 'learningRoadmapData',
      THEME: 'theme',
      USER_PREFERENCES: 'userPreferences'
    },

    // 主题
    THEMES: {
      LIGHT: 'light',
      DARK: 'dark'
    },

    // 项目类型
    ITEM_TYPES: {
      SECTION: 'section',
      SUBSECTION: 'subsection',
      ITEM: 'item'
    },

    // 侧边栏显示层级
    SIDEBAR_MAX_LEVEL: 2,

    // 计时器更新间隔（毫秒）
    TIMER_INTERVAL: 1000,

    // 动画持续时间
    ANIMATION_DURATION: 300,

    // 搜索防抖延迟
    SEARCH_DEBOUNCE_DELAY: 300
  },

  // ===== CSS类名 =====
  CSS_CLASSES: {
    // 状态类
    ACTIVE: 'active',
    COMPLETED: 'completed',
    COLLAPSED: 'collapsed',
    SELECTED: 'selected',
    DISABLED: 'disabled',
    HIDDEN: 'hidden',
    LOADING: 'loading',

    // 组件类
    TREE_SECTION: 'tree-section',
    TREE_SUBSECTION: 'tree-subsection',
    TREE_ITEM: 'tree-item',
    TREE_CHILDREN: 'tree-children',
    TREE_NODE_HEADER: 'tree-node-header',
    TREE_NODE_CONTENT: 'tree-node-content',

    // 按钮类
    BTN_PRIMARY: 'btn-primary',
    BTN_SECONDARY: 'btn-secondary',
    BTN_ICON: 'btn-icon',
    BTN_CHECKBOX: 'btn-checkbox',
    BTN_TIMER: 'btn-timer',

    // 拖拽状态
    DRAGOVER: 'dragover'
  },

  // ===== 错误消息 =====
  ERROR_MESSAGES: {
    FILE_TYPE_ERROR: '请选择 Markdown (.md) 或文本 (.txt) 文件',
    FILE_SIZE_ERROR: '文件大小超过限制',
    PARSE_ERROR: '文件解析失败，请检查文件格式',
    STORAGE_ERROR: '数据保存失败',
    LOAD_ERROR: '数据加载失败'
  },

  // ===== 成功消息 =====
  SUCCESS_MESSAGES: {
    FILE_UPLOADED: '文件上传成功',
    DATA_SAVED: '数据保存成功',
    NOTES_SAVED: '笔记已保存！',
    PROGRESS_RESET: '进度重置成功'
  },

  // ===== 确认消息 =====
  CONFIRM_MESSAGES: {
    RESET_PROGRESS: '确定要重置所有学习进度吗？此操作不可撤销！',
    CLEAR_CONTENT: '确定要清空所有内容吗？'
  },

  // ===== 默认文本 =====
  DEFAULT_TEXT: {
    EMPTY_STATE_TITLE: '暂无学习路线',
    EMPTY_STATE_DESC: '请上传 Markdown 文件来生成您的学习路线',
    NO_SEARCH_RESULTS: '未找到匹配结果',
    CURRENT_ITEM_PLACEHOLDER: '未选择',
    WELCOME_TITLE: '欢迎使用学习路线管理系统',
    WELCOME_DESC: '点击左侧课程节点查看详细内容'
  },

  // ===== 类型标签 =====
  TYPE_LABELS: {
    section: '📚 章节',
    subsection: '📖 小节',
    item: '📄 项目'
  },

  // ===== 图标映射 =====
  ICONS: {
    UPLOAD: '📁',
    FILE: '📄',
    STATS: '📊',
    COMPLETED: '✅',
    TIME: '⏱️',
    PROGRESS: '🎯',
    ROADMAP: '🗺️',
    SEARCH: '🔍',
    COURSE: '📚',
    DETAIL: '📖',
    TIMER: '⏰',
    CLOSE: '✕',
    EXPAND: '▶',
    COLLAPSE: '▼',
    CHECKMARK: '✓',
    MOON: '🌙',
    SUN: '☀️'
  }
};
