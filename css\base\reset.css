/* ===== CSS重置和基础样式 ===== */

/* 现代CSS重置 */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 移除默认样式 */
html {
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transition: var(--transition);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 标题重置 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* 段落和文本 */
p {
  margin: 0;
  line-height: var(--line-height-relaxed);
}

/* 链接 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 列表 */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* 表格 */
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

th, td {
  padding: var(--spacing-sm);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

/* 表单元素 */
input, button, textarea, select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

input, textarea, select {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm);
  color: var(--text-primary);
  transition: var(--transition);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

input:disabled, textarea:disabled, select:disabled {
  background-color: var(--bg-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

/* 按钮重置 */
button {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  transition: var(--transition);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 图片和媒体 */
img, video, canvas, audio, iframe, embed, object {
  display: block;
  max-width: 100%;
  height: auto;
}

img {
  border-style: none;
}

/* 代码 */
code, kbd, samp, pre {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

code {
  background-color: var(--bg-secondary);
  padding: 2px 4px;
  border-radius: 4px;
  color: var(--text-primary);
}

pre {
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  overflow-x: auto;
  border: 1px solid var(--border-color);
}

pre code {
  background: none;
  padding: 0;
}

/* 引用 */
blockquote {
  margin: 0;
  padding: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
  background-color: var(--bg-secondary);
  font-style: italic;
  color: var(--text-secondary);
}

/* 分割线 */
hr {
  border: none;
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-lg) 0;
}

/* 隐藏元素 */
[hidden] {
  display: none !important;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 跳过链接（无障碍） */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: var(--z-index-tooltip);
  transition: var(--transition);
}

.skip-link:focus {
  top: 6px;
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-light);
  color: var(--text-primary);
}

::-moz-selection {
  background-color: var(--primary-light);
  color: var(--text-primary);
}

/* 滚动条样式（Webkit） */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Firefox滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) var(--bg-secondary);
}

/* 打印样式 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a, a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  pre, blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr, img {
    page-break-inside: avoid;
  }

  img {
    max-width: 100% !important;
  }

  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }

  h2, h3 {
    page-break-after: avoid;
  }
}
